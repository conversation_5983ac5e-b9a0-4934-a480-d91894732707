import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";

// Schema for creating/updating departments
const departmentSchema = z.object({
  name: z.string().min(1, "שם המחלקה נדרש").max(100),
  description: z.string().optional(),
  employerId: z.string().uuid(),
});

// Schema for filtering departments
const filterDepartmentsSchema = z.object({
  employerId: z.string().uuid(),
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
});

export const departmentRouter = createTRPCRouter({
  // Get all departments with filtering and pagination
  getAll: protectedProcedure
    .input(filterDepartmentsSchema)
    .query(async ({ ctx, input }) => {
      const { employerId, search, page, limit } = input;
      
      // Ensure user has access to this employer's data
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      const where = {
        employerId,
        tenantId: user.tenantId,
        ...(search && {
          name: {
            contains: search,
            mode: "insensitive" as const,
          },
        }),
      };

      const [departments, total] = await Promise.all([
        ctx.db.department.findMany({
          where,
          take: limit,
          skip: (page - 1) * limit,
          orderBy: { name: "asc" },
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                employees: true,
              },
            },
          },
        }),
        ctx.db.department.count({ where }),
      ]);

      return {
        departments,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // Get a single department by ID
  getById: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input: id }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      const department = await ctx.db.department.findFirst({
        where: {
          id,
          tenantId: user.tenantId,
        },
        include: {
          _count: {
            select: {
              employees: true,
            },
          },
        },
      });

      if (!department) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Department not found",
        });
      }

      return department;
    }),

  // Create a new department
  create: protectedProcedure
    .input(departmentSchema)
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      // Check if department with same name exists for this employer
      const existing = await ctx.db.department.findFirst({
        where: {
          name: input.name,
          employerId: input.employerId,
          tenantId: user.tenantId,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "מחלקה בשם זה כבר קיימת",
        });
      }

      const department = await ctx.db.department.create({
        data: {
          ...input,
          tenantId: user.tenantId,
        },
      });

      return department;
    }),

  // Update an existing department
  update: protectedProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        data: departmentSchema.partial(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, data } = input;

      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      // Check if department exists and belongs to user's tenant
      const existingDepartment = await ctx.db.department.findFirst({
        where: {
          id,
          tenantId: user.tenantId,
        },
      });

      if (!existingDepartment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Department not found",
        });
      }

      // If name is being updated, check for duplicates
      if (data.name && data.name !== existingDepartment.name) {
        const duplicate = await ctx.db.department.findFirst({
          where: {
            name: data.name,
            employerId: existingDepartment.employerId,
            tenantId: user.tenantId,
            id: { not: id },
          },
        });

        if (duplicate) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "מחלקה בשם זה כבר קיימת",
          });
        }
      }

      const updatedDepartment = await ctx.db.department.update({
        where: { id },
        data,
      });

      return updatedDepartment;
    }),

  // Delete a department
  delete: protectedProcedure
    .input(z.string().uuid())
    .mutation(async ({ ctx, input: id }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      // Check if department exists and belongs to user's tenant
      const department = await ctx.db.department.findFirst({
        where: {
          id,
          tenantId: user.tenantId,
        },
        include: {
          _count: {
            select: {
              employees: true,
            },
          },
        },
      });

      if (!department) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Department not found",
        });
      }

      // Check if department has employees
      if (department._count.employees > 0) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "לא ניתן למחוק מחלקה שיש לה עובדים",
        });
      }

      await ctx.db.department.delete({
        where: { id },
      });

      return { success: true };
    }),
});
