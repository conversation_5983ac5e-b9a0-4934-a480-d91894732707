import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { Role } from "@prisma/client";

const updateSchema = z.object({
  allowedCategories: z.array(z.string()),
});

export const documentSettingsRouter = createTRPCRouter({
  getSettings: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: { tenantId: true },
    });
    if (!user) {
      throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
    }
    const settings = await ctx.db.documentSettings.findUnique({
      where: { tenantId: user.tenantId },
    });
    return {
      allowedCategories: settings?.allowedCategories ?? ["form-101"],
    };
  }),
  updateSettings: protectedProcedure
    .input(updateSchema)    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true, role: true },
      });
      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }
      if (user.role !== Role.OWNER) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const updated = await ctx.db.documentSettings.upsert({
        where: { tenantId: user.tenantId },
        create: { tenantId: user.tenantId, allowedCategories: input.allowedCategories },
        update: { allowedCategories: input.allowedCategories },
      });
      return { allowedCategories: updated.allowedCategories };
    }),
});
