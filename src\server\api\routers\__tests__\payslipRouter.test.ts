import { describe, expect, it, vi } from "vitest";
process.env.SKIP_ENV_VALIDATION = "true";
vi.mock("@/server/auth", () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import("../../trpc");
const { payslipRouter } = await import("../payslip");
const createCaller = createCallerFactory(payslipRouter);

const baseCtx = {
	db: {},
	logger: undefined,
	headers: new Headers(),
} as unknown as Record<string, unknown>;

describe("payslipRouter.calculate", () => {
	it("throws when employee not found", async () => {
		const dbMock = {
			employee: { findUnique: vi.fn().mockResolvedValue(null) },
		} as unknown as Record<string, unknown>;
		const caller = createCaller({
			...baseCtx,
			db: dbMock,
			session: { user: { id: "u1" } },
		});
		await expect(
			caller.calculate({ employeeId: "e1", month: 5, year: 2024 }),
		).rejects.toBeInstanceOf(Error);
	});

	it("creates payslip with transactions", async () => {
		const dbMock = {
			employee: {
				findUnique: vi
					.fn()
					.mockResolvedValue({ id: "e1", tenantId: "t1", baseSalary: 1000 }),
			},
			salaryTransaction: {
				findMany: vi.fn().mockResolvedValue([
					{
						id: "t1",
						amount: 100,
						description: "ot",
						rate: null,
						quantity: null,
						percentage: null,
					},
					{
						id: "t2",
						amount: -50,
						description: "ded",
						rate: null,
						quantity: null,
						percentage: null,
					},
				]),
				updateMany: vi.fn().mockResolvedValue({}),
			},
			payslip: {
				create: vi.fn().mockResolvedValue({ id: "p1" }),
			},
			$transaction: vi.fn(async (fn) => fn(dbMock)),
		} as unknown as Record<string, unknown>;
		const caller = createCaller({
			...baseCtx,
			db: dbMock,
			session: { user: { id: "u1" } },
		});
		const res = await caller.calculate({
			employeeId: "e1",
			month: 5,
			year: 2024,
		});
		expect(dbMock.payslip.create).toHaveBeenCalled();
		expect(res).toEqual({ id: "p1" });
	});
});
