import { describe, it, expect } from 'vitest';
import { calculateIncomeTax } from '@/utils/payroll-calculations';

describe('calculateIncomeTax', () => {
  it('applies 223₪ per credit point', () => {
    const res = calculateIncomeTax(10000, 1);
    expect(res.taxCreditsValue).toBe(223);
  });

  it('calculates tax correctly with credits', () => {
    const res = calculateIncomeTax(10000, 2.25);
    expect(res.finalTax).toBeCloseTo(974.515, 3);
  });
});
