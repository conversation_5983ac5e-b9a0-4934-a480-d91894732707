import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Role } from "@prisma/client";
import crypto from "crypto";

// Define Zod schemas for tenant operations
export const getTenantSchema = z.object({
  page: z.number().default(1),
  limit: z.number().default(10),
});

export const createTenantSchema = z.object({
  name: z.string().min(3).max(100),
  plan: z.string().optional(),
});

export const updateTenantSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(3).max(100).optional(),
  plan: z.string().optional(),
});

export const tenantRouter = createTRPCRouter({
  // Get all tenants with pagination (admin only)
  getAll: protectedProcedure
    .input(getTenantSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;        // Get the current user's role - only OWNER can list all tenants
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true, role: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Only OWNER can see all tenants, ADMIN can only see their own
        if (user.role !== "OWNER") {
          // If ADMIN, only return their own tenant
          if (user.role === "ADMIN") {
            const tenant = await db.tenant.findUnique({
              where: { id: user.tenantId },
              include: {
                _count: {
                  select: {
                    users: true,
                    employers: true,
                    employees: true
                  }
                }
              }
            });
            if (!tenant) {
              throw new TRPCError({
                code: "NOT_FOUND",
                message: "Tenant not found",
              });
            }
            return {
              tenants: [
                {
                  id: tenant.id,
                  name: tenant.name,
                  plan: tenant.plan || "Free",
                  userCount: tenant._count.users,
                  employerCount: tenant._count.employers,
                  employeeCount: tenant._count.employees,
                  createdAt: tenant.createdAt.toISOString(),
                  updatedAt: tenant.updatedAt.toISOString()
                }
              ],
              totalCount: 1,
              pageCount: 1
            };
          }
          throw new TRPCError({
            code: "FORBIDDEN", 
            message: "Only system owners can access all tenants"
          });
        }
        
        // Calculate pagination
        const skip = (input.page - 1) * input.limit;
        
        // Get total count for pagination
        const totalCount = await db.tenant.count();
        
        // Get tenants with user and employer counts
        const tenants = await db.tenant.findMany({
          skip,
          take: input.limit,
          include: {
            _count: {
              select: {
                users: true,
                employers: true,
                employees: true
              }
            }
          },
          orderBy: {
            name: "asc"
          }
        });
        
        return {
          tenants: tenants.map(tenant => ({
            id: tenant.id,
            name: tenant.name,
            plan: tenant.plan || "Free",
            userCount: tenant._count.users,
            employerCount: tenant._count.employers,
            employeeCount: tenant._count.employees,
            createdAt: tenant.createdAt.toISOString(),
            updatedAt: tenant.updatedAt.toISOString()
          })),
          totalCount,
          pageCount: Math.ceil(totalCount / input.limit)
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error fetching tenants");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch tenants",
          cause: error,
        });
      }
    }),
    
  // Get tenant by ID (only accessible if user belongs to the tenant or is owner)
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
          // Get the current user's role and tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true, role: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Only OWNER can access any tenant, ADMIN can only access their own
        const isOwner = user.role === "OWNER";
        const isAdminOfOwnTenant = user.role === "ADMIN" && user.tenantId === input.id;
        const isMemberOfTenant = user.tenantId === input.id;
        
        if (!isOwner && !isAdminOfOwnTenant && !isMemberOfTenant) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You don't have permission to access this tenant",
          });
        }
        
        // Get tenant with associated counts
        const tenant = await db.tenant.findUnique({
          where: { id: input.id },
          include: {
            _count: {
              select: {
                users: true,
                employers: true,
                employees: true
              }
            }
          }
        });
        
        if (!tenant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Tenant not found",
          });
        }
        
        // Get current active usage metrics
        const activeEmployees = await db.employee.count({
          where: {
            tenantId: tenant.id,
            status: "ACTIVE"
          }
        });
        
        const currentMonthPayslips = await db.payslip.count({
          where: {
            tenantId: tenant.id,
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1
          }
        });
        
        return {
          id: tenant.id,
          name: tenant.name,
          plan: tenant.plan || "Free",
          stats: {
            users: tenant._count.users,
            employers: tenant._count.employers,
            employees: tenant._count.employees,
            activeEmployees,
            currentMonthPayslips
          },
          createdAt: tenant.createdAt.toISOString(),
          updatedAt: tenant.updatedAt.toISOString()
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id, tenantId: input.id }, "Error fetching tenant");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch tenant details",
          cause: error,
        });
      }
    }),
    
  // Create a new tenant (admin only)
  create: protectedProcedure
    .input(createTenantSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the current user's role - only OWNER/ADMIN can create tenants
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { role: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if the user has permission to create tenants
        if (user.role !== "OWNER" && user.role !== "ADMIN") {
          throw new TRPCError({
            code: "FORBIDDEN", 
            message: "Only administrators can create tenants"
          });
        }
        
        // Check if tenant with the same name already exists
        const existingTenant = await db.tenant.findFirst({
          where: { name: { equals: input.name, mode: "insensitive" } }
        });
        
        if (existingTenant) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "A tenant with this name already exists",
          });
        }
        
        // Generate a secure random password for the default admin
        const adminPassword = crypto.randomBytes(16).toString("base64url");
        
        // Create the tenant
        const tenant = await db.tenant.create({
          data: {
            name: input.name,
            plan: input.plan,
            users: {
              create: [
                {
                  email: `admin@${input.name.toLowerCase().replace(/\s/g, "-")}.com`,
                  name: "Default Admin",
                  role: "ADMIN",
                  password: adminPassword, // Secure random password
                  isActive: true,
                  mustResetPassword: true // Force password reset on first login
                }
              ]
            }
          }
        });
          // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: tenant.id,
            userId: userId,
            action: "CREATE",
            modelName: "Tenant",
            recordId: tenant.id,
            values: {
              create: [
                {
                  valueType: "new",
                  fieldName: "name",
                  fieldValue: input.name,
                  dataType: "string"
                },
                ...(input.plan ? [{
                  valueType: "new" as const,
                  fieldName: "plan",
                  fieldValue: input.plan,
                  dataType: "string"
                }] : [])
              ]
            }
          }
        });
        
        return {
          id: tenant.id,
          name: tenant.name,
          plan: tenant.plan || "Free",
          createdAt: tenant.createdAt.toISOString()
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error creating tenant");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create tenant",
          cause: error,
        });
      }
    }),
    
  // Update a tenant (admin only)
  update: protectedProcedure
    .input(updateTenantSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
          // Get the current user's role - only OWNER/ADMIN can update tenants
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true, role: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Only OWNER can update any tenant, ADMIN can only update their own
        const isOwner = user.role === "OWNER";
        const isAdminOfOwnTenant = user.role === "ADMIN" && user.tenantId === input.id;
        if (!isOwner && !isAdminOfOwnTenant) {
          throw new TRPCError({
            code: "FORBIDDEN", 
            message: "You don't have permission to update this tenant"
          });
        }
        
        // Get the existing tenant to capture old values for audit log
        const existingTenant = await db.tenant.findUnique({
          where: { id: input.id }
        });
        
        if (!existingTenant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Tenant not found",
          });
        }
        
        // Prepare update data
        const updateData: any = {};
        if (input.name !== undefined) {
          // Check if the new name is already taken
          if (input.name !== existingTenant.name) {
            const nameExists = await db.tenant.findFirst({
              where: {
                name: { equals: input.name, mode: "insensitive" },
                id: { not: input.id }
              }
            });
            if (nameExists) {
              throw new TRPCError({
                code: "CONFLICT",
                message: "A tenant with this name already exists",
              });
            }
          }
          updateData.name = input.name;
        }
        
        if (input.plan !== undefined) {
          updateData.plan = input.plan;
        }
        
        // Update the tenant
        const updatedTenant = await db.tenant.update({
          where: { id: input.id },
          data: updateData
        });
          // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: updatedTenant.id,
            userId: userId,
            action: "UPDATE",
            modelName: "Tenant",
            recordId: updatedTenant.id,
            values: {
              create: [
                {
                  valueType: "old",
                  fieldName: "name",
                  fieldValue: existingTenant.name,
                  dataType: "string"
                },
                ...(updateData.name ? [{
                  valueType: "new" as const,
                  fieldName: "name",
                  fieldValue: updateData.name,
                  dataType: "string"
                }] : []),
                {
                  valueType: "old",
                  fieldName: "plan",
                  fieldValue: existingTenant.plan || "",
                  dataType: "string"
                },
                ...(updateData.plan !== undefined ? [{
                  valueType: "new" as const,
                  fieldName: "plan",
                  fieldValue: updateData.plan || "",
                  dataType: "string"
                }] : [])
              ]
            }
          }
        });
        
        return {
          id: updatedTenant.id,
          name: updatedTenant.name,
          plan: updatedTenant.plan || "Free",
          updatedAt: updatedTenant.updatedAt.toISOString()
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id, tenantId: input.id }, "Error updating tenant");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update tenant",
          cause: error,
        });
      }
    }),
    
  // Delete a tenant (system owner only)
  deleteTenant: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
          // Get the current user's role - only OWNER can delete tenants
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true, role: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if the user has permission to delete tenants
        if (user.role !== "OWNER") {
          throw new TRPCError({
            code: "FORBIDDEN", 
            message: "Only system owners can delete tenants"
          });
        }
        
        // Get the existing tenant for audit log
        const existingTenant = await db.tenant.findUnique({
          where: { id: input.id },
          include: {
            _count: {
              select: {
                users: true,
                employers: true,
                employees: true
              }
            }
          }
        });
        
        if (!existingTenant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Tenant not found",
          });
        }
        
        // Prevent deletion of tenant with active data
        if (existingTenant._count.employers > 0 || existingTenant._count.employees > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot delete tenant with active employers or employees",
          });
        }
          // Create audit log entry before deletion
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId, // Log to the user's tenant, not the one being deleted
            userId: userId,
            action: "DELETE",
            modelName: "Tenant",
            recordId: input.id,
            values: {
              create: [
                {
                  valueType: "old",
                  fieldName: "id",
                  fieldValue: existingTenant.id,
                  dataType: "string"
                },
                {
                  valueType: "old",
                  fieldName: "name",
                  fieldValue: existingTenant.name,
                  dataType: "string"
                },
                {
                  valueType: "old",
                  fieldName: "plan",
                  fieldValue: existingTenant.plan || "",
                  dataType: "string"
                }
              ]
            }
          }
        });
        
        // Delete the tenant - this will cascade delete all related records
        await db.tenant.delete({
          where: { id: input.id }
        });
        
        return { success: true };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id, tenantId: input.id }, "Error deleting tenant");
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete tenant",
          cause: error,
        });
      }
    }),
});
            