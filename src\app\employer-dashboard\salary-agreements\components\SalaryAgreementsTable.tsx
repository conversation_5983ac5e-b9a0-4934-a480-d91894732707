"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { Edit, Trash } from "lucide-react";
import type { SalaryAgreement } from "../types";

interface Props {
	agreements: SalaryAgreement[];
	isLoading: boolean;
	onEdit: (agreement: SalaryAgreement) => void;
	onDelete: (agreement: SalaryAgreement) => void;
}

export default function SalaryAgreementsTable({
	agreements,
	isLoading,
	onEdit,
	onDelete,
}: Props) {
	if (isLoading) {
		return <Skeleton className="h-48 w-full" />;
	}

	return (
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>שם</TableHead>
					<TableHead>תיאור</TableHead>
					<TableHead>תקף מ</TableHead>
					<TableHead>תקף עד</TableHead>
					<TableHead />
				</TableRow>
			</TableHeader>
			<TableBody>
				{agreements.map((ag) => (
					<TableRow key={ag.id} data-testid="agreement-row">
						<TableCell>{ag.name}</TableCell>
						<TableCell>{ag.description}</TableCell>
						<TableCell>{ag.effectiveFrom?.toString().slice(0, 10)}</TableCell>
						<TableCell>{ag.effectiveTo?.toString().slice(0, 10)}</TableCell>
						<TableCell className="flex justify-end gap-2">
							<Button
								variant="ghost"
								size="icon"
								onClick={() => onEdit(ag)}
								aria-label="edit"
							>
								<Edit className="h-4 w-4" />
							</Button>
							<Button
								variant="ghost"
								size="icon"
								onClick={() => onDelete(ag)}
								aria-label="delete"
							>
								<Trash className="h-4 w-4" />
							</Button>
						</TableCell>
					</TableRow>
				))}
				{agreements.length === 0 && (
					<TableRow>
						<TableCell colSpan={5} className="text-center">
							אין נתונים
						</TableCell>
					</TableRow>
				)}
			</TableBody>
		</Table>
	);
}
