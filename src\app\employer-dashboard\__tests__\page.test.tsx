// @vitest-environment node

import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';

// Prepare mocks for the tRPC hooks used by the page
var hooks = {
  useEmployerUsers: vi.fn(),
  useEmployerAuditLogs: vi.fn(),
};

// Stub the employees table component so we can verify the employerId prop
var EmployeesTableMock = vi.fn(() => <div data-testid="employees-table" />);

vi.mock('next-auth/react', () => ({
  useSession: vi.fn(),
}));

vi.mock('../hooks', () => ({
  useEmployerUsers: (...args: any[]) => hooks.useEmployerUsers(...args),
  useEmployerAuditLogs: (...args: any[]) => hooks.useEmployerAuditLogs(...args),
}));

vi.mock('../components/employer-employees-table', () => ({
  EmployerEmployeesTable: EmployeesTableMock,
}));

import EmployerDashboardPage from '../page';
import { useSession } from 'next-auth/react';

describe('EmployerDashboardPage', () => {
  beforeEach(() => {
    // Default session mock
    (useSession as unknown as vi.Mock).mockReturnValue({
      data: { user: { employerId: 'emp1' } },
      status: 'authenticated',
    });

    hooks.useEmployerUsers.mockReturnValue({
      users: [
        {
          id: 'u1',
          name: 'Jane Doe',
          email: '<EMAIL>',
          role: 'ADMIN',
          employerId: 'emp1',
          employerName: 'ACME',
          status: 'active',
        },
      ],
      isLoading: false,
      createUser: vi.fn(),
      isCreating: false,
    });

    hooks.useEmployerAuditLogs.mockReturnValue({ logs: [], isLoading: false });

    EmployeesTableMock.mockClear();
  });

  it('uses employer ID from session', () => {
    render(<EmployerDashboardPage />);

    expect(hooks.useEmployerUsers).toHaveBeenCalledWith(1, 'emp1');
    expect(hooks.useEmployerAuditLogs).toHaveBeenCalledWith(1, 'all', 'emp1');
    expect(EmployeesTableMock).toHaveBeenCalledWith(
      expect.objectContaining({ employerId: 'emp1' }),
      expect.anything(),
    );
  });

  it('renders users table with returned data', () => {
    render(<EmployerDashboardPage />);

    expect(screen.getByText('Jane Doe')).toBeInTheDocument();
    expect(screen.getByTestId('employees-table')).toBeInTheDocument();
  });
});
