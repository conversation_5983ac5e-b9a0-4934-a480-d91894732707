"use client";

import { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { he } from "date-fns/locale";
import { CalendarIcon, Download, Loader2 } from "lucide-react";
import { usePaymentChangeReport } from "../hooks";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface ChangeReportModalProps {
  open: boolean;
  onClose: () => void;
}

export default function ChangeReportModal({
  open,
  onClose,
}: ChangeReportModalProps) {
  const [fromDate, setFromDate] = useState<Date | undefined>(
    new Date(new Date().setMonth(new Date().getMonth() - 1))
  );
  const [toDate, setToDate] = useState<Date | undefined>(new Date());

  const { data: report, isLoading } = usePaymentChangeReport(
    fromDate!,
    toDate!
  );

  const handleExport = () => {
    if (!report) return;

    const csvContent = [
      ["תאריך", "סוג שינוי", "רכיב", "שדה", "ערך קודם", "ערך חדש", "משתמש"],
      ...(report.changes || []).map((change: any) => [
        format(new Date(change.timestamp), "dd/MM/yyyy HH:mm"),
        change.action,
        change.componentName,
        change.field,
        change.oldValue || "",
        change.newValue || "",
        change.userName || "",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob(["\ufeff" + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `payment-changes-${format(
      fromDate!,
      "yyyy-MM-dd"
    )}-to-${format(toDate!, "yyyy-MM-dd")}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getActionBadge = (action: string) => {
    switch (action) {
      case "CREATE":
        return <Badge variant="default">יצירה</Badge>;
      case "UPDATE":
        return <Badge variant="secondary">עדכון</Badge>;
      case "DELETE":
        return <Badge variant="destructive">מחיקה</Badge>;
      default:
        return <Badge variant="outline">{action}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>דוח שינויים - רכיבי תשלום</DialogTitle>
          <DialogDescription>
            הצג את כל השינויים שבוצעו ברכיבי התשלום בתקופה הנבחרת
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Date Range Selection */}
          <div className="flex gap-4 items-end">
            <div className="space-y-2">
              <label className="text-sm font-medium">מתאריך</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[200px] justify-start text-right",
                      !fromDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {fromDate ? (
                      format(fromDate, "dd/MM/yyyy")
                    ) : (
                      <span>בחר תאריך</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={fromDate}
                    onSelect={setFromDate}
                    locale={he}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">עד תאריך</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[200px] justify-start text-right",
                      !toDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {toDate ? (
                      format(toDate, "dd/MM/yyyy")
                    ) : (
                      <span>בחר תאריך</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={toDate}
                    onSelect={setToDate}
                    locale={he}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <Button
              onClick={handleExport}
              variant="outline"
              disabled={!report || report.changes.length === 0}
            >
              <Download className="h-4 w-4 ml-2" />
              ייצוא לאקסל
            </Button>
          </div>

          {/* Summary */}
          {report && (
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-900">
                  {report.summary.totalChanges}
                </div>
                <div className="text-sm text-blue-700">סה"כ שינויים</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-900">
                  {report.summary.createdCount}
                </div>
                <div className="text-sm text-green-700">רכיבים חדשים</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-yellow-900">
                  {report.summary.updatedCount}
                </div>
                <div className="text-sm text-yellow-700">רכיבים שעודכנו</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-900">
                  {report.summary.deletedCount}
                </div>
                <div className="text-sm text-red-700">רכיבים שנמחקו</div>
              </div>
            </div>
          )}

          {/* Changes Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>תאריך ושעה</TableHead>
                  <TableHead>סוג שינוי</TableHead>
                  <TableHead>רכיב</TableHead>
                  <TableHead>שדה</TableHead>
                  <TableHead>ערך קודם</TableHead>
                  <TableHead>ערך חדש</TableHead>
                  <TableHead>משתמש</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell colSpan={7}>
                        <Skeleton className="h-8 w-full" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : report?.changes.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      className="text-center py-8 text-gray-500"
                    >
                      לא נמצאו שינויים בתקופה הנבחרת
                    </TableCell>
                  </TableRow>
                ) : (
                  report?.changes.map((change: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell>
                        {format(
                          new Date(change.timestamp),
                          "dd/MM/yyyy HH:mm"
                        )}
                      </TableCell>
                      <TableCell>{getActionBadge(change.action)}</TableCell>
                      <TableCell className="font-medium">
                        {change.componentName}
                      </TableCell>
                      <TableCell>{change.field || "-"}</TableCell>
                      <TableCell>{change.oldValue || "-"}</TableCell>
                      <TableCell>{change.newValue || "-"}</TableCell>
                      <TableCell>{change.userName || "-"}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            סגור
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
