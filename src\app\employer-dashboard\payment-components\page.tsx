"use client";

import { useState } from "react";
import { usePaymentComponents } from "./hooks";
import PaymentComponentsTable from "./components/PaymentComponentsTable";
import PaymentFormModal from "./components/PaymentFormModal";
import ChangeReportModal from "./components/ChangeReportModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, FileText } from "lucide-react";
import type { PaymentComponent, PaymentComponentFilters } from "./types";

export default function PaymentComponentsPage() {
  const [filters, setFilters] = useState<PaymentComponentFilters>({});
  const [selectedComponent, setSelectedComponent] = useState<PaymentComponent | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isReportOpen, setIsReportOpen] = useState(false);

  const { data: components = [], isLoading } = usePaymentComponents(filters);

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search }));
  };

  const handleAdd = () => {
    setSelectedComponent(null);
    setIsFormOpen(true);
  };

  const handleEdit = (component: PaymentComponent) => {
    setSelectedComponent(component);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setSelectedComponent(null);
    setIsFormOpen(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">מרכיבי תשלום</h1>
        <Button
          onClick={() => setIsReportOpen(true)}
          variant="outline"
        >
          <FileText className="h-4 w-4 ml-2" />
          דוח שינויים
        </Button>
      </div>

      {/* Actions Bar */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="חיפוש לפי שם או קוד..."
              className="pr-10"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 ml-2" />
          הוספת רכיב תשלום
        </Button>
      </div>

      {/* Table */}
      <PaymentComponentsTable
        components={components as PaymentComponent[]}
        isLoading={isLoading}
        onEdit={handleEdit}
        onFiltersChange={setFilters}
        filters={filters}
      />

      {/* Modals */}
      <PaymentFormModal
        open={isFormOpen}
        onClose={handleCloseForm}
        paymentComponent={selectedComponent}
      />

      <ChangeReportModal
        open={isReportOpen}
        onClose={() => setIsReportOpen(false)}
      />
    </div>
  );
} 
