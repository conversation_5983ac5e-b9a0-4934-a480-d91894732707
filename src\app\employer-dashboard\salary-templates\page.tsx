"use client";

import { useState } from "react";
import { useSalaryTemplates } from "./hooks";
import SalaryTemplatesTable from "./components/SalaryTemplatesTable";
import SalaryTemplateFormModal from "./components/SalaryTemplateFormModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus } from "lucide-react";
import type { SalaryTemplate } from "./types";

export default function SalaryTemplatesPage() {
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState<SalaryTemplate | null>(null);
  const [open, setOpen] = useState(false);

  const { data: templates = [], isLoading } = useSalaryTemplates();

  const filtered = templates.filter(t =>
    t.name.toLowerCase().includes(search.toLowerCase())
  );

  const handleAdd = () => {
    setSelected(null);
    setOpen(true);
  };

  const handleEdit = (t: SalaryTemplate) => {
    setSelected(t);
    setOpen(true);
  };

  const handleClose = () => {
    setSelected(null);
    setOpen(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">תבניות שכר</h1>
      </div>
      <div className="flex gap-4 items-center">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="חיפוש לפי שם..."
              className="pr-10"
              onChange={e => setSearch(e.target.value)}
            />
          </div>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 ml-2" />
          הוספת תבנית
        </Button>
      </div>
      <SalaryTemplatesTable templates={filtered as SalaryTemplate[]} isLoading={isLoading} onEdit={handleEdit} />
      <SalaryTemplateFormModal open={open} onClose={handleClose} template={selected} />
    </div>
  );
}
