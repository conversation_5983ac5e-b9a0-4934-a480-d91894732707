import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import type { Prisma } from "@prisma/client";

// Input validation schemas
const createLocationSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  address: z.string().optional(),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  radius: z.number().min(1).max(10000).optional(), // max 10km
  timezone: z.string().default("Asia/Jerusalem"),
  isActive: z.boolean().default(true),
});

const updateLocationSchema = createLocationSchema.extend({
  id: z.string(),
});

export const locationRouter = createTRPCRouter({
  // Get all locations
  getAll: publicProcedure
    .input(
      z.object({
        isActive: z.boolean().optional(),
      }).optional()
    )
    .query(async ({ ctx, input }) => {
      const where: Prisma.LocationWhereInput = { deletedAt: null };
      if (input?.isActive !== undefined) {
        where.isActive = input.isActive;
      }
      
      return ctx.db.location.findMany({
        where,
        orderBy: {
          name: "asc",
        },
      });
    }),

  // Get location by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const location = await ctx.db.location.findUnique({
        where: { id: input.id },
      });

      if (!location || location.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "מיקום לא נמצא",
        });
      }

      return location;
    }),
  // Create new location
  create: protectedProcedure
    .input(createLocationSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if code already exists
      const existing = await ctx.db.location.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קוד מיקום כבר קיים במערכת",
        });
      }

      // Validate coordinates if provided
      if ((input.latitude && !input.longitude) || (!input.latitude && input.longitude)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "יש לספק גם קו רוחב וגם קו אורך",        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      return ctx.db.location.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update location
  update: publicProcedure
    .input(updateLocationSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // Check if location exists
      const existing = await ctx.db.location.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "מיקום לא נמצא",
        });
      }

      // Check if new code conflicts with another location
      if (data.code !== existing.code) {
        const codeExists = await ctx.db.location.findFirst({
          where: {
            code: data.code,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (codeExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "קוד מיקום כבר קיים במערכת",
          });
        }
      }

      // Validate coordinates if provided
      if ((data.latitude && !data.longitude) || (!data.latitude && data.longitude)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "יש לספק גם קו רוחב וגם קו אורך",
        });
      }

      return ctx.db.location.update({
        where: { id },
        data,
      });
    }),

  // Delete location (soft delete)
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if location exists
      const existing = await ctx.db.location.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "מיקום לא נמצא",
        });
      }

      // Check if location is in use
      const inUse = await ctx.db.movement.findFirst({
        where: {
          locationId: input.id,
          deletedAt: null,
        },
      });

      if (inUse) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "לא ניתן למחוק מיקום שנמצא בשימוש בתנועות",
        });
      }

      return ctx.db.location.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
}); 