{"name": "salary-t3", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev", "postinstall": "prisma generate", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "seed:admin": "tsx scripts/seed-admin.ts", "add:seed": "tsx scripts/seed.ts"}, "dependencies": {"@ai-sdk/vercel": "^0.0.1", "@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@faker-js/faker": "^9.8.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.4", "@tanstack/react-query": "^5.77.0", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.1.2", "@trpc/react-query": "^11.1.2", "@trpc/server": "^11.1.2", "ai": "^4.3.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "framer": "^2.4.1", "framer-motion": "^12.12.2", "gsap": "^3.13.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "motion": "^12.12.2", "next": "^15.3.2", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "pino": "^9.7.0", "prisma-erd-generator": "^2.0.4", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-motion": "^0.5.2", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "server-only": "^0.0.1", "sonner": "^2.0.3", "superjson": "^2.2.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.1.7", "@testing-library/react": "^16.3.0", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "pino-pretty": "^13.0.0", "postcss": "^8.5.3", "prisma": "^6.8.2", "tailwindcss": "^4.1.7", "trpc-ui": "^1.0.15", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.11.0"}