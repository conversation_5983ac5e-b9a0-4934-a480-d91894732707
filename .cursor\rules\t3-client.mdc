---
description: 
globs: 
alwaysApply: false
---
- **<PERSON><PERSON> Schema Definition**
  - Define schemas in a separate file or section
  - Use descriptive names for schemas
  - Leverage <PERSON><PERSON>'s composition features
  ```typescript
  // ✅ DO: Define clear, reusable schemas
  export const userSchema = z.object({
    id: z.string().uuid(),
    name: z.string().min(2).max(50),
    email: z.string().email(),
    role: z.enum(["user", "admin"]),
    metadata: z.record(z.string()).optional(),
  });
  
  // ✅ DO: Create derived schemas through composition
  export const createUserSchema = userSchema
    .omit({ id: true })
    .extend({ password: z.string().min(8) });
  
  // ❌ DON'T: Repeat schema definitions
  export const userUpdateSchema = z.object({
    name: z.string().min(2).max(50),
    email: z.string().email(),
    // Duplicating definitions that already exist in userSchema
  });
  ```

- **API Hooks Organization**
  - Group related hooks in dedicated files
  - Use consistent naming conventions
  - Provide clear documentation for complex hooks
  ```typescript
  // ✅ DO: Organize hooks by domain/entity
  // src/hooks/user-hooks.ts
  export const useUsers = () => {
    return api.user.getAll.useQuery();
  };
  
  export const useUser = (id: string) => {
    return api.user.getById.useQuery({ id });
  };
  
  // ❌ DON'T: Mix unrelated hooks
  // src/hooks/misc-hooks.ts
  export const useProducts = () => { /* ... */ };
  export const useUsers = () => { /* ... */ };
  export const useSettings = () => { /* ... */ };
  ```

- **useQuery Implementation**
  - Provide descriptive query keys
  - Handle loading and error states
  - Specify appropriate staleTime and refetchInterval
  ```typescript
  // ✅ DO: Implement useQuery with proper options
  const { data, isLoading, error } = api.user.getById.useQuery(
    { id },
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
      onError: (err) => {
        toast.error(`Failed to fetch user: ${err.message}`);
      },
    }
  );
  
  // ✅ DO: Handle loading and error states
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!data) return <EmptyState />;
  
  // ❌ DON'T: Use queries without proper error handling
  const { data } = api.user.getById.useQuery({ id });
  // Immediately using data without checks
  return <div>{data.name}</div>; // May cause runtime errors!
  ```

- **useMutation Implementation**
  - Handle loading, error, and success states
  - Invalidate related queries on success
  - Provide optimistic updates when appropriate
  ```typescript
  // ✅ DO: Implement useMutation with proper callbacks
    const utils = api.useUtils();
  const mutation = api.user.update.useMutation({
    onSuccess: () => {
      // Invalidate and refetch related queries
      utils.user.getAll.invalidate();
      utils.user.getById.invalidate({ id });
      toast.success("User updated successfully");
    },
    onError: (error) => {
      toast.error(`Update failed: ${error.message}`);
    },
  });
  
  // ✅ DO: Handle mutation states in UI
  <Button
    onClick={() => mutation.mutate(formData)}
    disabled={mutation.isLoading}
  >
    {mutation.isLoading ? "Updating..." : "Update User"}
  </Button>
  
  // ❌ DON'T: Neglect to handle loading/error states
  const mutation = api.user.update.useMutation();
  <Button onClick={() => mutation.mutate(formData)}>
    Update User
  </Button>
  ```

- **Optimistic Updates**
  - Implement optimistic updates for better UX
  - Handle rollback on errors
  - Update local cache immediately for responsive UI
  ```typescript
  // ✅ DO: Implement optimistic updates
    const utils = api.useUtils();
  const addTodoMutation = api.todo.add.useMutation({
    onMutate: async (newTodo) => {
      // Cancel outgoing refetches
      await utils.todo.getAll.cancel();
      
      // Snapshot the previous value
      const previousTodos = utils.todo.getAll.getData();
      
      // Optimistically update the cache
      utils.todo.getAll.setData(undefined, (old) => [
        ...(old || []),
        { id: 'temp-id', ...newTodo, createdAt: new Date() },
      ]);
      
      return { previousTodos };
    },
    onError: (err, newTodo, context) => {
      // Restore previous data on error
      utils.todo.getAll.setData(undefined, context?.previousTodos);
      toast.error("Failed to create todo");
    },
    onSettled: () => {
      // Always refetch after error or success
      utils.todo.getAll.invalidate();
    },
  });
  
  // ❌ DON'T: Make UI wait for server response when unnecessary
  const addTodoMutation = api.todo.add.useMutation({
    onSuccess: () => {
      utils.todo.getAll.invalidate();
      toast.success("Todo added");
    },
  });
  ```

- **Query Invalidation Patterns**
  - Invalidate specific queries
  - Use specific invalidation rather than broad invalidation
  - Consider query relationships when invalidating
  ```typescript
  // ✅ DO: Invalidate specific queries
    const utils = api.useUtils();
  api.post.create.useMutation({
    onSuccess: (newPost) => {
      // Invalidate the specific post
      utils.post.getById.invalidate({ id: newPost.id });
      
      // Invalidate lists containing this post
      utils.post.getAll.invalidate();
      utils.post.getByCategory.invalidate({ category: newPost.category });
    }
  });
  
  // ❌ DON'T: Over-invalidate
    const utils = api.useUtils();
  api.post.create.useMutation({
    onSuccess: () => {
      // This invalidates ALL queries, which is excessive
      utils.invalidate();
    }
  });
  ```

- **Error Handling**
  - Create consistent error handling patterns
  - Display user-friendly error messages
  - Log detailed errors for debugging
  ```typescript
  // ✅ DO: Implement consistent error handling
  const { data, error } = api.user.getById.useQuery(
    { id },
    {
      onError: (err) => {
        // Log detailed error for debugging
        console.error("Detailed error:", err);
        
        // Show user-friendly message
        toast.error(formatErrorMessage(err));
      }
    }
  );
  
  // Helper function for consistent error formatting
  const formatErrorMessage = (err: unknown): string => {
    if (err instanceof TRPCClientError) {
      if (err.data?.code === 'UNAUTHORIZED') {
        return 'Please log in to continue';
      }
      return err.message;
    }
    return 'An unexpected error occurred';
  };
  
  // ❌ DON'T: Expose raw error messages or use inconsistent formats
  onError: (err) => {
    toast.error(`Error: ${err}`);
  }
  ```

- **Form Integration**
  - Integrate Zod schemas with form libraries
  - Maintain type safety throughout the form submission process
  - Reuse validation schemas
  ```typescript
  // ✅ DO: Use Zod with React Hook Form
  import { zodResolver } from "@hookform/resolvers/zod";
  import { useForm } from "react-hook-form";
  
  // Reuse the same schema from API validation
  const userCreateSchema = api.user.create.getInputSchema();
  
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(userCreateSchema),
    defaultValues: {
      name: "",
      email: "",
    },
  });
  
  const createUser = api.user.create.useMutation();
  
  const onSubmit = handleSubmit((data) => {
    createUser.mutate(data);
  });
  
  // ❌ DON'T: Define separate validation logic in forms
  const { register, handleSubmit } = useForm();
  
  const onSubmit = handleSubmit((data) => {
    // Manual validation duplicates schema logic
    if (data.name.length < 2) {
      setError("name", { message: "Name too short" });
      return;
    }
    // ...more manual validation
    createUser.mutate(data);
  });
  ```

- **Infinite Queries**
  - Implement infinite scrolling with useInfiniteQuery
  - Include proper loading indicators for next pages
  - Handle errors during fetching more data
  ```typescript
  // ✅ DO: Implement proper infinite queries
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status
  } = api.post.getInfinite.useInfiniteQuery(
    { limit: 10 },
    {
      getNextPageParam: (lastPage) => lastPage.nextCursor,
    }
  );
  
  // UI implementation
  <div>
    {status === 'loading' ? (
      <LoadingSpinner />
    ) : status === 'error' ? (
      <ErrorMessage />
    ) : (
      <>
        {data.pages.map((page, i) => (
          <React.Fragment key={i}>
            {page.items.map((item) => (
              <PostCard key={item.id} post={item} />
            ))}
          </React.Fragment>
        ))}
        <div>
          <Button
            onClick={() => fetchNextPage()}
            disabled={!hasNextPage || isFetchingNextPage}
          >
            {isFetchingNextPage
              ? 'Loading more...'
              : hasNextPage
              ? 'Load More'
              : 'Nothing more to load'}
          </Button>
        </div>
      </>
    )}
  </div>
  
  // ❌ DON'T: Mix pagination logic in components
  const [page, setPage] = useState(1);
  const { data, isLoading } = api.post.getPage.useQuery({ page, limit: 10 });
  
  // Manual pagination implementation duplicates logic
  ```

- **Prefetching Strategies**
  - Prefetch critical data for performance
  - Use hover-based prefetching for anticipated interactions
  - Implement cursor-based pagination for lists
  ```typescript
  // ✅ DO: Prefetch on hover
    const utils = api.useUtils();
  
  const prefetchPost = async (id: string) => {
    await utils.post.getById.prefetch({ id });
  };
  
  return (
    <Link 
      href={`/posts/${post.id}`}
      onMouseEnter={() => prefetchPost(post.id)}
    >
      {post.title}
    </Link>
  );
  
  // ✅ DO: Prefetch next page in lists
  useEffect(() => {
    if (data && !isLoading && hasNextPage) {
      void utils.post.getInfinite.prefetchInfinite({
        limit: 10,
        cursor: data.pages[data.pages.length - 1].nextCursor,
      });
    }
  }, [data, isLoading]);
  
  // ❌ DON'T: Overfetch with unnecessary prefetching
  // Prefetching every item in a large list
  items.forEach((item) => {
    utils.item.getById.prefetch({ id: item.id });
  });
  ```

