"use client";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import SalaryAgreementFormModal from "./components/SalaryAgreementFormModal";
import SalaryAgreementsTable from "./components/SalaryAgreementsTable";
import { useDeleteSalaryAgreement, useSalaryAgreements } from "./hooks";
import type { SalaryAgreement } from "./types";

export default function SalaryAgreementsPage() {
	const { data: agreements = [], isLoading } = useSalaryAgreements();
	const deleteMutation = useDeleteSalaryAgreement();
	const [selected, setSelected] = useState<SalaryAgreement | null>(null);
	const [open, setOpen] = useState(false);

	const handleAdd = () => {
		setSelected(null);
		setOpen(true);
	};

	const handleEdit = (ag: SalaryAgreement) => {
		setSelected(ag);
		setOpen(true);
	};

	const handleDelete = async (ag: SalaryAgreement) => {
		if (confirm(`למחוק את ההסכם ${ag.name}?`)) {
			await deleteMutation.mutateAsync({ id: ag.id });
		}
	};

	return (
		<div className="container mx-auto space-y-6 p-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-3xl">הסכמי שכר</h1>
				<Button onClick={handleAdd}>
					<Plus className="ml-2 h-4 w-4" /> הוספת הסכם
				</Button>
			</div>
			<SalaryAgreementsTable
				agreements={agreements}
				isLoading={isLoading}
				onEdit={handleEdit}
				onDelete={handleDelete}
			/>
			<SalaryAgreementFormModal
				open={open}
				onClose={() => setOpen(false)}
				agreement={selected}
			/>
		</div>
	);
}
