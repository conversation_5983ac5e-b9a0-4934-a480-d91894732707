"use client";

import { api } from "@/trpc/react";
import { toast } from "sonner";

export function useSalaryAgreements(employerId?: string) {
	return api.salaryAgreement.getAll.useQuery({ employerId });
}

export function useSalaryAgreement(id: string) {
	return api.salaryAgreement.getById.useQuery({ id }, { enabled: !!id });
}

export function useCreateSalaryAgreement() {
	  const utils = api.useUtils();
	return api.salaryAgreement.create.useMutation({
		onSuccess: () => {
			utils.salaryAgreement.getAll.invalidate();
			toast.success("הסכם נוצר בהצלחה");
		},
	});
}

export function useUpdateSalaryAgreement() {
	  const utils = api.useUtils();
	return api.salaryAgreement.update.useMutation({
		onSuccess: (data) => {
			utils.salaryAgreement.getAll.invalidate();
			utils.salaryAgreement.getById.invalidate({ id: data.id });
			toast.success("הסכם עודכן בהצלחה");
		},
	});
}

export function useDeleteSalaryAgreement() {
	  const utils = api.useUtils();
	return api.salaryAgreement.delete.useMutation({
		onSuccess: () => {
			utils.salaryAgreement.getAll.invalidate();
			toast.success("הסכם נמחק");
		},
	});
}
