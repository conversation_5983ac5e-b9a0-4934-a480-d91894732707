import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

export interface PayslipItem {
  description: string;
  amount: string | number;
}

export interface PayslipPdfData {
  employeeName: string;
  nationalId: string;
  period: string; // e.g. "5/2024"
  items: PayslipItem[];
  netPay: string | number;
}

export async function generatePayslipPDF(data: PayslipPdfData): Promise<Buffer> {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([595.28, 841.89]); // A4
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

  const { width, height } = page.getSize();
  let y = height - 50;

  page.drawText(`Payslip - ${data.period}`, { x: 50, y, size: 18, font });
  y -= 30;
  page.drawText(`Employee: ${data.employeeName}`, { x: 50, y, size: 12, font });
  y -= 20;
  page.drawText(`ID: ${data.nationalId}`, { x: 50, y, size: 12, font });
  y -= 30;

  for (const item of data.items) {
    page.drawText(`${item.description}: ${item.amount}`, { x: 50, y, size: 12, font });
    y -= 20;
  }

  y -= 10;
  page.drawText(`Net Pay: ${data.netPay}`, { x: 50, y, size: 14, font, color: rgb(0, 0, 1) });

  const bytes = await pdfDoc.save();
  return Buffer.from(bytes);
}

