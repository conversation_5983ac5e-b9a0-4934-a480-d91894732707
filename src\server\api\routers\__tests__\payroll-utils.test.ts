import { describe, expect, it } from "vitest";
process.env.SKIP_ENV_VALIDATION = "true";

import {
	MINIMUM_WAGE_2025,
	NI_RATES_2025,
	calculateNationalInsurance,
	calculateOvertimePay,
	validateMinimumWageCompliance,
	validatePayslipConsistency,
} from "@/utils/payroll-calculations";

describe("calculateOvertimePay", () => {
	it("computes overtime pay for valid hours", () => {
		const res = calculateOvertimePay(160, 10, 5, 2, 1, 50);
		expect(res.isValid).toBe(true);
		expect(res.totalPay).toBeCloseTo(9275);
		expect(res.errors).toHaveLength(0);
	});

	it("flags excessive hours and low wage", () => {
		const res = calculateOvertimePay(200, 90, 61, 41, 21, 20);
		expect(res.isValid).toBe(false);
		expect(res.errors.length).toBeGreaterThan(0);
	});
});

describe("calculateNationalInsurance", () => {
	it("calculates contributions for resident", () => {
		const res = calculateNationalInsurance(10000, true, 30);
		expect(res.employeeNI).toBeCloseTo(10000 * NI_RATES_2025.employee.resident);
		expect(res.employerNI).toBeCloseTo(10000 * NI_RATES_2025.employer.resident);
		expect(res.healthInsurance).toBeCloseTo(10000 * NI_RATES_2025.health);
		expect(res.totalNI).toBeCloseTo(
			res.employeeNI + res.employerNI + res.healthInsurance,
		);
	});

	it("handles non-resident above retirement age", () => {
		const res = calculateNationalInsurance(10000, false, 70);
		expect(res.employeeNI).toBe(0);
		expect(res.employerNI).toBeCloseTo(
			10000 * NI_RATES_2025.employer.nonResident,
		);
		expect(res.totalNI).toBeCloseTo(res.employerNI + res.healthInsurance);
	});
});

describe("validateMinimumWageCompliance", () => {
	it("passes for hourly wage above minimum", () => {
		const res = validateMinimumWageCompliance(5000, 160, "HOURLY");
		expect(res.isCompliant).toBe(true);
		expect(res.shortfall).toBe(0);
		expect(res.hourlyRate).toBeCloseTo(31.25);
	});

	it("fails for monthly salary below minimum", () => {
		const res = validateMinimumWageCompliance(5000, 186, "MONTHLY");
		expect(res.isCompliant).toBe(false);
		expect(res.minimumRequired).toBe(MINIMUM_WAGE_2025.monthly);
		expect(res.shortfall).toBe(MINIMUM_WAGE_2025.monthly - 5000);
	});
});

describe("validatePayslipConsistency", () => {
	it("validates consistent payslip", () => {
		const res = validatePayslipConsistency(10000, 8500, 1000, 500);
		expect(res.isConsistent).toBe(true);
		expect(res.variance).toBeLessThanOrEqual(1);
		expect(res.errors).toHaveLength(0);
	});

	it("detects inconsistent and negative net pay", () => {
		const res = validatePayslipConsistency(2000, -100, 1500, 1000);
		expect(res.isConsistent).toBe(false);
		expect(res.errors.length).toBeGreaterThan(0);
	});
});
