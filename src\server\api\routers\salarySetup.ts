// filepath: c:\Users\<USER>\Downloads\salary-t3\src\server\api\routers\salarySetup.ts
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import {
  DeductionType,
  TaxCalculationType,
  SocialSecurityCalculationType,
  PaymentType,
  PaymentGroup,
  AssociationType,
} from "@prisma/client";

/**
 * Router for setting up the salary structure components
 * This includes creating basic payment and deduction components,
 * templates, agreements, and associations
 */
export const salarySetupRouter = createTRPCRouter({
  /**
   * Create basic payment and deduction components
   */
  createBasicComponents: protectedProcedure
    .input(
      z.object({
        includeOvertime: z.boolean().default(true),
        includeSickLeave: z.boolean().default(true),
        includeHolidays: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { includeOvertime, includeSickLeave, includeHolidays } = input;
      const { db, session } = ctx;

      // Get tenant and employer information
      const user = await db.user.findUnique({
        where: { id: session.user.id },
        select: { tenantId: true, employerId: true },
      });

      if (!user?.tenantId || !user?.employerId) {
        throw new Error("User must be associated with a tenant and employer");
      }

      const tenantId = user.tenantId;
      const employerId = user.employerId;

      // Create basic payment components
      await db.paymentComponent.createMany({
        data: [
          // Base salary component
          {
            tenantId,
            employerId,
            code: "BASE",
            name: "שכר בסיס",
            description: "שכר בסיס חודשי",
            paymentType: PaymentType.OTHER,
            paymentGroup: PaymentGroup.BASIC,
            taxCalculation: TaxCalculationType.TAX_LIABLE,
            socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
            affectsPension: true,
            isOneTime: false,
            isActive: true,
          },
          // Transportation allowance
          {
            tenantId,
            employerId,
            code: "TRANS",
            name: "הוצאות נסיעה",
            description: "החזר הוצאות נסיעה",
            paymentType: PaymentType.ALLOWANCE,
            paymentGroup: PaymentGroup.SUPPLEMENT,
            taxCalculation: TaxCalculationType.TAX_EXEMPT,
            socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
            affectsPension: false,
            isOneTime: false,
            isActive: true,
          },
          // Recovery pay
          {
            tenantId,
            employerId,
            code: "RECOV",
            name: "דמי הבראה",
            description: "תשלום דמי הבראה",
            paymentType: PaymentType.ALLOWANCE,
            paymentGroup: PaymentGroup.SUPPLEMENT,
            taxCalculation: TaxCalculationType.TAX_LIABLE,
            socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
            affectsPension: true,
            isOneTime: false,
            isActive: true,
          },
        ],
      });

      // Add overtime components if requested
      if (includeOvertime) {
        await db.paymentComponent.createMany({
          data: [
            // 125% overtime
            {
              tenantId,
              employerId,
              code: "OT125",
              name: "שעות נוספות 125%",
              description: "תשלום עבור שעות נוספות בתעריף 125%",
              paymentType: PaymentType.ALLOWANCE, // FIXED: Changed from OVERTIME
              paymentGroup: PaymentGroup.SUPPLEMENT, 
              taxCalculation: TaxCalculationType.TAX_LIABLE,
              socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
              affectsPension: true,
              isOneTime: false,
              isActive: true,
            },
            // 150% overtime
            {
              tenantId,
              employerId,
              code: "OT150",
              name: "שעות נוספות 150%",
              description: "תשלום עבור שעות נוספות בתעריף 150%",
              paymentType: PaymentType.ALLOWANCE, // FIXED: Changed from OVERTIME 
              paymentGroup: PaymentGroup.SUPPLEMENT,
              taxCalculation: TaxCalculationType.TAX_LIABLE,
              socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
              affectsPension: true,
              isOneTime: false,
              isActive: true,
            },
          ],
        });
      }

      // Add sick leave components if requested
      if (includeSickLeave) {
        await db.paymentComponent.createMany({
          data: [
            {
              tenantId,
              employerId,
              code: "SICK",
              name: "ימי מחלה",
              description: "תשלום עבור ימי מחלה",
              paymentType: PaymentType.ALLOWANCE,
              paymentGroup: PaymentGroup.SUPPLEMENT,
              taxCalculation: TaxCalculationType.TAX_LIABLE,
              socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
              affectsPension: true,
              isOneTime: false,
              isActive: true,
            },
          ],
        });
      }

      // Add holiday components if requested
      if (includeHolidays) {
        await db.paymentComponent.createMany({
          data: [
            {
              tenantId,
              employerId,
              code: "HOL",
              name: "ימי חג",
              description: "תשלום עבור ימי חג",
              paymentType: PaymentType.ALLOWANCE,
              paymentGroup: PaymentGroup.SUPPLEMENT,
              taxCalculation: TaxCalculationType.TAX_LIABLE,
              socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
              affectsPension: true,
              isOneTime: false,
              isActive: true,
            },
            {
              tenantId,
              employerId,
              code: "VAC",
              name: "ימי חופשה",
              description: "תשלום עבור ימי חופשה",
              paymentType: PaymentType.ALLOWANCE,
              paymentGroup: PaymentGroup.SUPPLEMENT,
              taxCalculation: TaxCalculationType.TAX_LIABLE,
              socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
              affectsPension: true,
              isOneTime: false,
              isActive: true,
            },
          ],
        });
      }

      // Create basic deduction components
      await db.deductionComponent.createMany({
        data: [
          // Income tax deduction
          {
            tenantId,
            employerId,
            code: "TAX",
            name: "מס הכנסה",
            description: "ניכוי מס הכנסה",
            deductionType: DeductionType.INCOME_TAX,
            taxCalculation: TaxCalculationType.TAX_EXEMPT,
            socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
            affectsPension: false,
            isOneTime: false,
            isActive: true,
          },
          // National insurance deduction
          {
            tenantId,
            employerId,
            code: "NI",
            name: "ביטוח לאומי",
            description: "ניכוי ביטוח לאומי",
            deductionType: DeductionType.NATIONAL_INSURANCE,
            taxCalculation: TaxCalculationType.TAX_EXEMPT,
            socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
            affectsPension: false,
            isOneTime: false,
            isActive: true,
          },
          // Health insurance deduction
          {
            tenantId,
            employerId,
            code: "HEALTH",
            name: "ביטוח בריאות",
            description: "ניכוי ביטוח בריאות",
            deductionType: DeductionType.HEALTH_INSURANCE,
            taxCalculation: TaxCalculationType.TAX_EXEMPT,
            socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
            affectsPension: false,
            isOneTime: false,
            isActive: true,
          },
          // Pension deduction
          {
            tenantId,
            employerId,
            code: "PENSION",
            name: "פנסיה",
            description: "ניכוי לפנסיה",
            deductionType: DeductionType.PENSION,
            taxCalculation: TaxCalculationType.TAX_EXEMPT,
            socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
            affectsPension: true,
            percentageOfSalary: 6.0,
            isOneTime: false,
            isActive: true,
          },
        ],
      });

      return { success: true };
    }),

  /**
   * Create salary templates
   */
  createTemplates: protectedProcedure
    .input(
      z.object({
        includeOvertime: z.boolean().default(true),
        includeSickLeave: z.boolean().default(true),
        includeHolidays: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { includeOvertime, includeSickLeave, includeHolidays } = input;
      const { db, session } = ctx;

      // Get tenant and employer information
      const user = await db.user.findUnique({
        where: { id: session.user.id },
        select: { tenantId: true, employerId: true },
      });

      if (!user?.tenantId || !user?.employerId) {
        throw new Error("User must be associated with a tenant and employer");
      }

      const tenantId = user.tenantId;
      const employerId = user.employerId;

      // Create basic salary template
      const basicTemplate = await db.salaryTemplate.create({
        data: {
          tenantId,
          employerId,
          name: "תבנית שכר בסיסית",
          description: "תבנית שכר סטנדרטית לעובדים",
          isActive: true,
          components: {
            baseSalary: { type: "FIXED", taxable: true },
            transportation: { amount: 300, type: "FIXED", taxable: false },
            recoveryPay: { amount: 423, type: "FIXED", taxable: true },
          },
        },
      });

      // Create foreign workers template
      const foreignWorkersTemplate = await db.salaryTemplate.create({
        data: {
          tenantId,
          employerId,
          name: "תבנית שכר לעובדים זרים",
          description: "תבנית שכר מותאמת לעובדים זרים",
          isActive: true,
          components: {
            baseSalary: { type: "FIXED", taxable: true },
            transportation: { amount: 300, type: "FIXED", taxable: false },
            recoveryPay: { amount: 423, type: "FIXED", taxable: true },
            mealAllowance: { amount: 300, type: "FIXED", taxable: false },
          },
        },
      });

      // Create managers template
      const managersTemplate = await db.salaryTemplate.create({
        data: {
          tenantId,
          employerId,
          name: "תבנית שכר למנהלים",
          description: "תבנית שכר מותאמת למנהלים",
          isActive: true,
          components: {
            baseSalary: { type: "FIXED", taxable: true },
            transportation: { amount: 500, type: "FIXED", taxable: false },
            recoveryPay: { amount: 423, type: "FIXED", taxable: true },
            managerBonus: { amount: 1000, type: "FIXED", taxable: true },
          },
        },
      });

      return {
        success: true,
        templates: {
          basicTemplate,
          foreignWorkersTemplate,
          managersTemplate,
        },
      };
    }),

  /**
   * Create salary and attendance agreements
   */
  createAgreements: protectedProcedure
    .input(
      z.object({
        includeOvertime: z.boolean().default(true),
        includeSickLeave: z.boolean().default(true),
        includeHolidays: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { includeOvertime, includeSickLeave, includeHolidays } = input;
      const { db, session } = ctx;

      // Get tenant and employer information
      const user = await db.user.findUnique({
        where: { id: session.user.id },
        select: { tenantId: true, employerId: true },
      });

      if (!user?.tenantId || !user?.employerId) {
        throw new Error("User must be associated with a tenant and employer");
      }

      const tenantId = user.tenantId;
      const employerId = user.employerId;

      // Create salary agreements
      const standardSalaryAgreement = await db.salaryAgreement.create({
        data: {
          tenantId,
          employerId,
          name: "הסכם שכר סטנדרטי",
          description: "הסכם שכר סטנדרטי לעובדים",
          effectiveFrom: new Date(),
          isActive: true,
          terms: {
            baseSalary: 6000,
            overtimeRate: 1.25,
            transportationAllowance: 300,
            workHoursPerDay: 8,
            workDaysPerMonth: 22,
          },
        },
      });

      // Create attendance agreements
      const standardAttendanceAgreement = await db.attendanceAgreement.create({
        data: {
          tenantId,
          code: "STD",
          name: "הסכם נוכחות סטנדרטי",
          description: "הסכם נוכחות סטנדרטי למשרה מלאה",
          status: "ACTIVE",
          workDaysPerWeek: 5,
          hoursPerDay: 8.4,
          monthlyHours: 182,
          overtimeThreshold: 8,
          nightShiftStart: "22:00",
          nightShiftEnd: "06:00",
          weekendDays: [5, 6], // Friday and Saturday
        },
      });

      const constructionAttendanceAgreement = await db.attendanceAgreement.create({
        data: {
          tenantId,
          code: "CONST",
          name: "הסכם נוכחות לעובדי בניין",
          description: "הסכם נוכחות לעובדי בניין במשרה מלאה",
          status: "ACTIVE",
          workDaysPerWeek: 6,
          hoursPerDay: 9,
          monthlyHours: 236,
          overtimeThreshold: 9,
          nightShiftStart: "22:00",
          nightShiftEnd: "06:00",
          weekendDays: [6], // Saturday only
        },
      });

      // Create overtime rules if requested
      if (includeOvertime) {
        await db.overtimeRule.createMany({
          data: [
            // Standard 125% rule
            {
              tenantId,
              agreementId: standardAttendanceAgreement.id,
              name: "שעות נוספות 125% - ימי חול",
              description: "2 שעות ראשונות ביום חול",
              fromHour: 8,
              toHour: 10,
              rate: 1.25,
              dayType: "WEEKDAY",
              priority: 1,
            },
            // Standard 150% rule
            {
              tenantId,
              agreementId: standardAttendanceAgreement.id,
              name: "שעות נוספות 150% - ימי חול",
              description: "מעל 2 שעות ביום חול",
              fromHour: 10,
              toHour: 24,
              rate: 1.5,
              dayType: "WEEKDAY",
              priority: 2,
            },
            // Construction 125% rule
            {
              tenantId,
              agreementId: constructionAttendanceAgreement.id,
              name: "שעות נוספות 125% - ימי חול",
              description: "2 שעות ראשונות ביום חול",
              fromHour: 9,
              toHour: 11,
              rate: 1.25,
              dayType: "WEEKDAY",
              priority: 1,
            },
            // Construction 150% rule
            {
              tenantId,
              agreementId: constructionAttendanceAgreement.id,
              name: "שעות נוספות 150% - ימי חול",
              description: "מעל 2 שעות ביום חול",
              fromHour: 11,
              toHour: 24,
              rate: 1.5,
              dayType: "WEEKDAY",
              priority: 2,
            },
          ],
        });
      }

      return {
        success: true,
        agreements: {
          standardSalaryAgreement,
          standardAttendanceAgreement,
          constructionAttendanceAgreement,
        },
      };
    }),

  /**
   * Create associations between employees and templates/agreements
   */
  createAssociations: protectedProcedure
    .input(z.object({}).optional())
    .mutation(async ({ ctx }) => {
      const { db, session } = ctx;

      // Get tenant information
      const user = await db.user.findUnique({
        where: { id: session.user.id },
        select: { tenantId: true },
      });

      if (!user?.tenantId) {
        throw new Error("User must be associated with a tenant");
      }

      const tenantId = user.tenantId;

      // Get all required entities for creating associations
      const [employees, departments, roles, salaryTemplates, salaryAgreements, attendanceAgreements] =
        await Promise.all([
          db.employee.findMany({ where: { tenantId } }),
          db.department.findMany({ where: { tenantId } }),
          db.employeeRole.findMany({ where: { tenantId } }),
          db.salaryTemplate.findMany({ where: { tenantId } }),
          db.salaryAgreement.findMany({ where: { tenantId } }),
          db.attendanceAgreement.findMany({ where: { tenantId } }),
        ]);

      if (
        !employees.length ||
        !departments.length ||
        !roles.length ||
        !salaryTemplates.length ||
        !salaryAgreements.length ||
        !attendanceAgreements.length
      ) {
        throw new Error("Missing required entities for creating associations");
      }

      // Create associations data array
      const associationsData = [];
      // Collect employee agreement data for batch insert
      const employeeAgreementsData = [];

      // Associate each employee with department, role, salary template, and salary agreement
      for (const employee of employees) {
        // Select department - use first department as fallback
        const department = departments.length > 0 ? departments[0] : null;

        // Select role - distribute employees among roles or use first role as fallback
        const role = roles.length > 0 ? roles[Math.floor(Math.random() * roles.length)] : null;

        // Select salary template - foreign workers get foreign template, others get basic
        const isForeign = employee.isForeign;
        const salaryTemplate = isForeign
          ? salaryTemplates.find((t) => t.name.includes("זרים")) || salaryTemplates[0]
          : salaryTemplates.find((t) => t.name.includes("בסיסית")) || salaryTemplates[0];

        // Select attendance agreement - construction workers get construction agreement, others get standard
        const isSectorConstruction = employee.sector === "CONSTRUCTION";
        const attendanceAgreement = isSectorConstruction
          ? attendanceAgreements.find((a) => a.name.includes("בניין")) || attendanceAgreements[0]
          : attendanceAgreements.find((a) => a.name.includes("סטנדרטי")) || attendanceAgreements[0];

        // Select salary agreement - use first as fallback
        const salaryAgreement = salaryAgreements[0];

        // Create department association
        if (department) {
          associationsData.push({
            tenantId,
            employeeId: employee.id,
            associationType: AssociationType.DEPARTMENT,
            departmentId: department.id,
            startDate: employee.startDate,
            notes: `Department association for ${employee.firstName} ${employee.lastName}`,
          });
        }

        // Create role association
        if (role) {
          associationsData.push({
            tenantId,
            employeeId: employee.id,
            associationType: AssociationType.ROLE,
            roleId: role.id,
            startDate: employee.startDate,
            notes: `Role association for ${employee.firstName} ${employee.lastName}`,
          });
        }

        // Create salary template association
        if (salaryTemplate) {
          associationsData.push({
            tenantId,
            employeeId: employee.id,
            associationType: AssociationType.SALARY_TEMPLATE,
            salaryTemplateId: salaryTemplate.id,
            startDate: employee.startDate,
            notes: `Salary template association for ${employee.firstName} ${employee.lastName}`,
          });
        }

        // Create salary agreement association
        if (salaryAgreement) {
          associationsData.push({
            tenantId,
            employeeId: employee.id,
            associationType: AssociationType.SALARY_AGREEMENT,
            salaryAgreementId: salaryAgreement.id,
            startDate: employee.startDate,
            notes: `Salary agreement association for ${employee.firstName} ${employee.lastName}`,
          });
        }

        // Collect employee agreement for attendance agreement
        if (attendanceAgreement) {
          employeeAgreementsData.push({
            tenantId,
            employeeId: employee.id,
            agreementId: attendanceAgreement.id,
            startDate: employee.startDate,
          });
        }
      }

      // Bulk create associations
      if (associationsData.length > 0) {
        await db.association.createMany({
          data: associationsData,
        });
      }

      // Bulk create employee agreements
      if (employeeAgreementsData.length > 0) {
        await db.employeeAgreement.createMany({
          data: employeeAgreementsData,
        });
      }

      return {
        success: true,
        associationsCreated: associationsData.length,
        employeeAgreementsCreated: employees.length,
      };
    }),
});
