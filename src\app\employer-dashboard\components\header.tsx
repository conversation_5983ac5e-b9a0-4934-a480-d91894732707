"use client";

import { useSession, signOut } from "next-auth/react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, LogOut, Bell } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

export function Header() {
	const { data: session } = useSession();

	// Get user display name and employer name
	const userName = session?.user?.name || "משתמש";
	const employerName = session?.user?.employerName || "מעסיק";
	const userEmail = session?.user?.email || "";

	// Get first letter for avatar fallback
	const avatarFallback = userName.charAt(0).toUpperCase();

	const handleLogout = async () => {
		await signOut({ callbackUrl: "/login" });
	};

	return (
		<header className="fixed top-0 left-0 right-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<div className="container flex h-16 items-center justify-between px-4">
				{/* Logo/Brand Area */}
				<div className="flex items-center space-x-2 rtl:space-x-reverse">
					<div className="relative flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-blue-400">
						<span className="font-bold text-white text-sm">S</span>
					</div>
					<div className="flex flex-col">
						<span className="text-sm font-semibold">לוח בקרה מעסיק</span>
						<span className="text-xs text-muted-foreground">{employerName}</span>
					</div>
				</div>

				{/* Right side - Profile and Actions */}
				<div className="flex items-center space-x-4 rtl:space-x-reverse">
					{/* Notifications */}
					<Button variant="ghost" size="icon" className="relative">
						<Bell className="h-5 w-5" />
						{/* Optional notification badge */}
						{/* <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500"></span> */}
					</Button>

					{/* User Profile Dropdown */}
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="ghost"
								className="relative h-8 w-8 rounded-full"
							>
								<Avatar className="h-8 w-8">
									<AvatarImage src="" alt={userName} />
									<AvatarFallback className="bg-blue-100 text-blue-600">
										{avatarFallback}
									</AvatarFallback>
								</Avatar>
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-56">
							<DropdownMenuLabel className="text-right">
								<div className="flex flex-col space-y-1">
									<p className="text-sm font-medium leading-none">{userName}</p>
									<p className="text-xs leading-none text-muted-foreground">
										{userEmail}
									</p>
									<p className="text-xs leading-none text-muted-foreground">
										{employerName}
									</p>
								</div>
							</DropdownMenuLabel>
							<DropdownMenuSeparator />
							<DropdownMenuItem className="text-right cursor-pointer">
								<User className="ml-2 h-4 w-4" />
								<span>פרופיל</span>
							</DropdownMenuItem>
							<DropdownMenuItem className="text-right cursor-pointer">
								<Settings className="ml-2 h-4 w-4" />
								<span>הגדרות</span>
							</DropdownMenuItem>
							<DropdownMenuSeparator />
							<DropdownMenuItem 
								className="text-right cursor-pointer text-red-600 focus:text-red-600"
								onClick={handleLogout}
							>
								<LogOut className="ml-2 h-4 w-4" />
								<span>התנתק</span>
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			</div>
		</header>
	);
}
