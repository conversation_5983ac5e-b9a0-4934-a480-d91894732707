import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));

vi.mock('@/server/s3', () => ({
  uploadFile: vi.fn().mockResolvedValue(undefined),
  getSignedDownloadUrl: vi.fn().mockResolvedValue('https://s3/payslip.pdf'),
  getPublicUrl: vi.fn((k:string)=>`https://s3/${k}`),
}));

vi.mock('@/server/sms', () => ({
  sendMessage: vi.fn().mockResolvedValue(true),
}));

vi.mock('@/server/utils/email', () => ({
  sendEmail: vi.fn().mockResolvedValue(undefined),
}));

const { createCallerFactory } = await import('../trpc');
const { employeeRouter } = await import('../employee');
const createCaller = createCallerFactory(employeeRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('employeeRouter.generatePayslipPDF', () => {
  it('uploads pdf and returns url', async () => {
    const { uploadFile, getSignedDownloadUrl } = await import('@/server/s3');
    const dbMock = {
      user: { findUnique: vi.fn().mockResolvedValue({ tenantId: 't1' }) },
      payslip: {
        findUnique: vi.fn().mockResolvedValue({
          id: 'p1',
          employeeId: 'e1',
          month: 5,
          year: 2024,
          netPay: 100,
          employee: { firstName: 'a', lastName: 'b', nationalId: '1', tenant: {} },
          items: [],
        }),
      },
      document: { create: vi.fn().mockResolvedValue({ id: 'd1' }) },
      documentToPayslip: { create: vi.fn().mockResolvedValue({}) },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock, session: { user: { id: 'u1' } } });
    const res = await caller.generatePayslipPDF({ payslipId: 'p1' });
    expect(uploadFile).toHaveBeenCalled();
    expect(getSignedDownloadUrl).toHaveBeenCalled();
    expect(res.pdfUrl).toBe('https://s3/payslip.pdf');
  });
});

describe('employeeRouter.sendPayslipToEmployee', () => {
  it('sends sms and email and updates status', async () => {
    const { sendMessage } = await import('@/server/sms');
    const { sendEmail } = await import('@/server/utils/email');
    const dbMock = {
      user: { findUnique: vi.fn().mockResolvedValue({ tenantId: 't1' }) },
      payslip: {
        findUnique: vi.fn().mockResolvedValue({
          id: 'p1',
          employeeId: 'e1',
          month: 5,
          year: 2024,
          employee: { firstName: 'a', contact: { phone: '050', email: '<EMAIL>' } },
        }),
        update: vi.fn().mockResolvedValue({ id: 'p1', employeeId: 'e1', employee: {} }),
      },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock, session: { user: { id: 'u1', email: '<EMAIL>' } } });
    const res = await caller.sendPayslipToEmployee({ payslipId: 'p1' });
    expect(sendMessage).toHaveBeenCalled();
    expect(sendEmail).toHaveBeenCalled();
    expect(dbMock.payslip.update).toHaveBeenCalled();
    expect(res.id).toBe('p1');
  });
});
