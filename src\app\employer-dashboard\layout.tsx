"use client";

import { redirect } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { type ReactNode, useState, createContext, useContext } from "react";
import { Sidebar } from "./components/sidebar";
import { Header } from "./components/header";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Bell, User, Settings, LogOut } from "lucide-react";
interface EmployerDashboardLayoutProps {
	children: ReactNode;
}

// Context for managing selected item across the dashboard
interface MasterDetailContextType {
	selectedItemId: string | null;
	setSelectedItemId: (id: string | null) => void;
	isDetailOpen: boolean;
	setIsDetailOpen: (open: boolean) => void;
}

const MasterDetailContext = createContext<MasterDetailContextType | undefined>(undefined);

export const useMasterDetail = () => {
	const context = useContext(MasterDetailContext);
	if (!context) {
		throw new Error("useMasterDetail must be used within MasterDetailProvider");
	}
	return context;
};

export default function EmployerDashboardLayout({
	children,
}: EmployerDashboardLayoutProps) {
	const { data: session, status } = useSession();
	const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
	const [isDetailOpen, setIsDetailOpen] = useState(false);

	if (status === "loading") {
		return (
			<div className="flex min-h-screen items-center justify-center">
				<div className="text-2xl font-medium">טוען...</div>
			</div>
		);
	}

	if (!session) {
		redirect("/login");
	}
	// Get user display name and employer name
	const userName = session?.user?.name || "משתמש";
	const employerName = session?.user?.employerName || "מעסיק";
	const userEmail = session?.user?.email || "";

	// Get first letter for avatar fallback
	const avatarFallback = userName.charAt(0).toUpperCase();

	const handleLogout = async () => {
		await signOut({ callbackUrl: "/login" });
	};
	return (
	<div className="flex min-h-screen flex-col">
		
		<MasterDetailContext.Provider value={{ selectedItemId, setSelectedItemId, isDetailOpen, setIsDetailOpen }}>
			
			<div className="flex min-h-screen flex-col">
			
				<div className="container flex-1 items-start pt-16">
					<div className={`grid h-[calc(100vh-4rem)] ${
						isDetailOpen 
							? "md:grid-cols-[220px_1fr_400px] lg:grid-cols-[240px_1fr_500px]" 
							: "md:grid-cols-[220px_1fr] lg:grid-cols-[240px_1fr]"
					} gap-6 transition-all duration-300`}>
						{/* Sidebar - Always visible on desktop */}
						<aside className="hidden md:block sticky top-16 z-40 h-[calc(100vh-4rem)] overflow-y-auto border-l">
						
							<Sidebar />
						</aside>
						
						{/* Master View - Main content */}
						<main className="flex w-full flex-col overflow-hidden">
							{children}
						</main>
						
						{/* Detail View - Conditionally rendered */}
						{isDetailOpen && (
							<aside className="hidden md:block sticky top-16 z-30 h-[calc(100vh-4rem)] overflow-y-auto border-l bg-gray-50 p-4 animate-in slide-in-from-left-5 duration-300">
								<div className="flex justify-between items-center mb-4">
									<h2 className="text-lg font-semibold">פרטים</h2>
									<button
										onClick={() => {
											setIsDetailOpen(false);
											setSelectedItemId(null);
										}}
										className="text-gray-500 hover:text-gray-700"
									>
										<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
										</svg>
									</button>
								</div>
								<div id="detail-content">
									{/* Detail content will be rendered here by child components */}
									<p className="text-gray-500">בחר פריט כדי להציג פרטים</p>
								</div>
							</aside>
						)}
					</div>
				</div>
			</div>
		</MasterDetailContext.Provider>
		</div>
	);
}

