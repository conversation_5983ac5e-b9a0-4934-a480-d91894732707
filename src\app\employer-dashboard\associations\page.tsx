"use client";

import React, { useState } from "react";
import { useSession } from "next-auth/react";
import { redirect, useParams, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAssociations, useAssociationsChangesReport } from "./hooks";
import type { AssociationType, Association } from "./hooks";
import { AssociationsTable, AssociationFormModal, AssociationsTabs } from "./components";
import { AlertCircle } from "lucide-react";
import { toast } from "sonner";

export default function AssociationsPage() {
  // State for search and filtering
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<AssociationType | "all">("all");
  const [startDate, setStartDate] = useState<string | undefined>(undefined);
  const [endDate, setEndDate] = useState<string | undefined>(undefined);
  const [page, setPage] = useState(1);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isCheckingIntegrity, setIsCheckingIntegrity] = useState(false);

  // Use session for auth
  const session = useSession();

  // Handle loading and authentication
  if (session.status === "loading") {
    return <div>טוען...</div>;
  }

  if (session.status === "unauthenticated") {
    redirect("/login");
  }

  // Get employer ID
  const params = useParams();
  const searchParams = useSearchParams();
  
  const employerId = params?.["employer-id"] as string || 
                     searchParams.get("employerId") || 
                     session.data?.user?.employerId || 
                     "";

  // Fetch associations data
  const {
    data: associationsData,
    isLoading,
    refetch
  } = useAssociations(
    page,
    employerId,
    selectedType === "all" ? undefined : selectedType as AssociationType,
    searchTerm,
    startDate,
    endDate
  );

  // Report generation
  const { mutate: generateReport, isPending: isGeneratingReport } = useAssociationsChangesReport(employerId);

  const handleGenerateReport = () => {
    generateReport({ employerId, startDate, endDate });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    refetch();
  };

  const handleTypeChange = (value: string) => {
    setSelectedType(value as AssociationType | "all");
    setPage(1);
    refetch();
  };

  const handleIntegrityCheck = async () => {
    setIsCheckingIntegrity(true);
    
    try {
      // בדיקת כל השיוכים
      const allAssociations = associationsData?.data || [];
      const issues: string[] = [];
        // בדיקת שיוכים חופפים
      for (let i = 0; i < allAssociations.length; i++) {
        for (let j = i + 1; j < allAssociations.length; j++) {
          const a1 = allAssociations[i];
          const a2 = allAssociations[j];
          
          // בדיקה שהאלמנטים קיימים ואם אותו עובד ואותו סוג שיוך
          if (a1 && a2 && a1.employeeId === a2.employeeId && a1.associationType === a2.associationType) {
            // בדיקת חפיפה בתאריכים
            const a1Start = new Date(a1.startDate);
            const a1End = a1.endDate ? new Date(a1.endDate) : null;
            const a2Start = new Date(a2.startDate);
            const a2End = a2.endDate ? new Date(a2.endDate) : null;
            
            let hasOverlap = false;
            
            if (!a1End && !a2End) {
              hasOverlap = true;
            } else if (!a1End) {
              hasOverlap = a2End ? a1Start <= a2End : true;
            } else if (!a2End) {
              hasOverlap = a1End >= a2Start;
            } else {
              hasOverlap = a1Start <= a2End && a1End >= a2Start;
            }
            
            if (hasOverlap) {
              issues.push(
                `חפיפה בשיוך: ${a1.employeeName} משויך ל${a1.associatedEntityName} ול${a2.associatedEntityName} באותה תקופה`
              );
            }
          }
        }
      }
      
      // בדיקת שיוכים ללא תאריך סיום כאשר יש שיוך חדש יותר
      const groupedByEmployeeAndType = allAssociations.reduce((acc: Record<string, Association[]>, assoc: Association) => {
        const key = `${assoc.employeeId}-${assoc.associationType}`;
        if (!acc[key]) acc[key] = [];
        acc[key].push(assoc);
        return acc;
      }, {});
      
      Object.values(groupedByEmployeeAndType).forEach((assocs) => {
        if (Array.isArray(assocs) && assocs.length > 1) {
          const sorted = assocs.sort((a, b) => 
            new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
          );
          
          for (let i = 0; i < sorted.length - 1; i++) {
            const current = sorted[i];
            if (current && !current.endDate) {
              issues.push(
                `שיוך פתוח: ${current.employeeName} - ${current.associatedEntityName} ללא תאריך סיום למרות שיוך חדש יותר`
              );
            }
          }
        }
      });
      
      // הצגת תוצאות
      if (issues.length === 0) {
        toast.success("בדיקת התקינות הושלמה - לא נמצאו בעיות");
      } else {
        toast.error(`נמצאו ${issues.length} בעיות בשיוכים`, {
          description: issues.slice(0, 3).join('\n') + (issues.length > 3 ? `\n...ועוד ${issues.length - 3} בעיות` : ''),
          duration: 10000,
        });
      }
    } catch (error) {
      toast.error("שגיאה בביצוע בדיקת תקינות");
      console.error(error);
    } finally {
      setIsCheckingIntegrity(false);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">שיוכים</h1>
        <div className="flex gap-2">
          <Button 
            onClick={handleIntegrityCheck} 
            disabled={isCheckingIntegrity || isLoading} 
            variant="outline"
          >
            {isCheckingIntegrity ? (
              <>
                <AlertCircle className="ml-2 h-4 w-4 animate-spin" />
                בודק...
              </>
            ) : (
              <>
                <AlertCircle className="ml-2 h-4 w-4" />
                בדיקות
              </>
            )}
          </Button>
          <Button onClick={handleGenerateReport} disabled={isGeneratingReport} variant="outline">
            {isGeneratingReport ? "מייצר דוח..." : "דוח שינויים"}
          </Button>
          <Button onClick={() => setIsAddModalOpen(true)}>הוספת שיוך</Button>
        </div>
      </div>

      {/* Search and filter */}
      <div className="bg-white rounded-lg shadow p-4">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-3">
          <div className="flex-1 min-w-[200px]">
            <Input
              placeholder="חיפוש לפי שם עובד, מחלקה, תפקיד..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="w-[150px]">
            <Select value={selectedType} onValueChange={handleTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="סוג שיוך" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">הכל</SelectItem>
                <SelectItem value="DEPARTMENT">מחלקות</SelectItem>
                <SelectItem value="ROLE">תפקידים</SelectItem>
                <SelectItem value="SALARY_TEMPLATE">תבניות שכר</SelectItem>
                <SelectItem value="SALARY_AGREEMENT">הסכמי שכר</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-[150px]">
            <Input 
              type="date"
              placeholder="מתאריך"
              value={startDate || ""}
              onChange={(e) => setStartDate(e.target.value || undefined)}
            />
          </div>
          <div className="w-[150px]">
            <Input 
              type="date"
              placeholder="עד תאריך"
              value={endDate || ""}
              onChange={(e) => setEndDate(e.target.value || undefined)}
            />
          </div>
          <Button type="submit">חפש</Button>
        </form>
      </div>

      {/* Main content area */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h2 className="text-lg font-medium">רשימת שיוכים</h2>
        </div>

        {/* Display tables based on filter type using tabs */}
        <AssociationsTabs
          associations={associationsData?.data || []}
          isLoading={isLoading}
          page={page}
          totalPages={associationsData?.meta.totalPages || 1}
          onPageChange={setPage}
          employerId={employerId}
          onRefresh={refetch}
          currentType={selectedType}
          onChangeType={handleTypeChange}
        />
      </div>

      {/* Add association modal */}
      <AssociationFormModal 
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        employerId={employerId}
        onSuccess={() => {
          setIsAddModalOpen(false);
          refetch();
        }}
      />
    </div>
  );
} 