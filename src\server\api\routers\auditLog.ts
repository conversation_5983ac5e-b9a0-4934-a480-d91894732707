import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import type { Prisma } from '@prisma/client';

export const auditLogRouter = createTRPCRouter({
  getLogs: protectedProcedure
    .input(
      z.object({
        page: z.number().default(1),
        limit: z.number().default(10),
        actionType: z
          .enum(["all", "create", "update", "delete"])
          .optional()
          .default("all"),
        employerId: z.string().optional(),
        userId: z.string().optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true },
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Calculate pagination
        const skip = (input.page - 1) * input.limit;

        // Build the where clause based on filters
        const where: Prisma.AuditLogWhereInput = {
          tenantId: user.tenantId,
        };

        // Add action type filter if not "all"
        if (input.actionType !== "all") {
          where.action = input.actionType.toUpperCase() as Prisma.AuditLogWhereInput['action'];
        }

        // Add employer filter by modelName and recordId
        if (input.employerId) {
          where.modelName = "Employer";
          where.recordId = input.employerId;
        }

        // Add user filter
        if (input.userId) {
          where.userId = input.userId;
        }

        // Add date range filters
        const timestampFilter: Prisma.DateTimeFilter = {};
        if (input.startDate) {
          const startDate = new Date(input.startDate);
          if (!isNaN(startDate.getTime())) {
            timestampFilter.gte = startDate;
          }
        }

        if (input.endDate) {
          const endDate = new Date(input.endDate);
          if (!isNaN(endDate.getTime())) {
            timestampFilter.lte = endDate;
          }
        }

        if (Object.keys(timestampFilter).length > 0) {
          where.timestamp = timestampFilter;
        }

        // Get total count for pagination
        const totalCount = await db.auditLog.count({ where });        // Get logs with related data
        const logs = await db.auditLog.findMany({
          where,
          select: {
            id: true,
            tenantId: true,
            timestamp: true,
            ipAddress: true,
            userId: true,
            modelName: true,
            recordId: true,
            action: true,
            userEmail: true,
            values: {
              where: { valueType: 'new' },
              select: {
                fieldName: true,
                fieldValue: true,
                dataType: true
              }
            },
            user: {
              select: {
                email: true,
              },
            },
          },
          orderBy: {
            timestamp: "desc",
          },
          skip,
          take: input.limit,
        });

        // Fetch employer names for logs related to employers
        const employerLogs = logs.filter(
          (log) => log.modelName === "Employer" && log.recordId
        );
        const employerIds = employerLogs.map((log) => log.recordId as string);

        let employerMap: Record<string, string> = {};
        if (employerIds.length > 0) {
          const employers = await db.employer.findMany({
            where: {
              id: {
                in: employerIds,
              },
            },
            select: {
              id: true,
              name: true,
            },
          });

          employerMap = employers.reduce((map, employer) => {
            map[employer.id] = employer.name;
            return map;
          }, {} as Record<string, string>);
        }

        return {          logs: logs.map((log) => ({
            id: log.id,
            timestamp: log.timestamp.toISOString(),
            userEmail: log.user?.email || log.userEmail || "מערכת",
            employerName:
              log.modelName === "Employer" && log.recordId
                ? employerMap[log.recordId] || "-"
                : "-",
            actionType: log.action.toLowerCase(),
            entityType: log.modelName,
            details: log.values.reduce((acc: Record<string, unknown>, val) => {
              if (val.fieldName && val.fieldValue) {
                acc[val.fieldName] = val.fieldValue;
              }
              return acc;
            }, {})
          })),
          totalCount,
          pageCount: Math.ceil(totalCount / input.limit),
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error fetching audit logs");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch audit logs",
          cause: error,
        });
      }
    }),

  getLogDetails: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const logId = input.id;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true },
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }        // Get log with related data
        const log = await db.auditLog.findFirst({
          where: {
            id: logId,
            tenantId: user.tenantId,
          },
          include: {
            values: {
              where: { valueType: 'new' },
              select: {
                fieldName: true,
                fieldValue: true,
                dataType: true
              }
            },
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        });

        if (!log) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Audit log not found",
          });
        }

        // Fetch employer data if needed
        let employerName = "-";
        let employerId = "";

        if (log.modelName === "Employer" && log.recordId) {
          const employer = await db.employer.findUnique({
            where: {
              id: log.recordId,
            },
            select: {
              id: true,
              name: true,
            },
          });

          if (employer) {
            employerName = employer.name;
            employerId = employer.id;
          }
        }        return {
          id: log.id,
          timestamp: log.timestamp.toISOString(),
          userEmail: log.user?.email || log.userEmail || "מערכת",
          userName: log.user?.name || "מערכת",
          employerName,
          employerId,
          actionType: log.action.toLowerCase(),
          entityType: log.modelName,
          ipAddress: log.ipAddress || "",
          userAgent: ((log as unknown) as { userAgent?: string }).userAgent || "",
          details: log.values.reduce((acc: Record<string, unknown>, val) => {
            if (val.fieldName && val.fieldValue) {
              acc[val.fieldName] = val.fieldValue;
            }
            return acc;
          }, {})
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error fetching audit log details");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch audit log details",
          cause: error,
        });
      }
    }),
});
