import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { api } from "@/trpc/react";
import { cn } from "@/lib/utils";
import { LeaveType } from "@prisma/client";
import { toast } from "sonner";

// Define interfaces for type safety
interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  nationalId: string | null;
  status: string;
  contact: any;
  startDate: Date;
  endDate: Date | null;
  baseSalary: any; // Prisma Decimal type
  profilePictureUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  email: string | null;
  phone: string | null;
  department: {
    id: string;
    name: string;
  } | null;
  latestPayslip: {
    period: string;
    grossSalary: any; // Prisma Decimal type
    netSalary: any; // Prisma Decimal type
    status: string;
  } | null;
  payslips: any[]; // Additional field from API
}

interface LeaveBalance {
  leaveType: LeaveType;
  entitlement: number;
  taken: number;
  balance: number;
}

// Schema for leave record form
const leaveRecordSchema = z.object({
  employeeId: z.string().uuid({
    message: "יש לבחור עובד",
  }),
  leaveType: z.nativeEnum(LeaveType, {
    errorMap: () => ({
      message: "יש לבחור סוג חופשה",
    }),
  }),
  startDate: z.date({
    required_error: "יש לבחור תאריך התחלה",
  }),
  endDate: z.date({
    required_error: "יש לבחור תאריך סיום",
  }),
  taken: z.coerce.number().positive({
    message: "מספר ימים חייב להיות חיובי",
  }),
  notes: z.string().optional(),
}).refine(data => data.endDate >= data.startDate, {
  message: "תאריך סיום חייב להיות אחרי תאריך התחלה",
  path: ["endDate"],
});

type LeaveRecordFormValues = z.infer<typeof leaveRecordSchema>;

const defaultValues: Partial<LeaveRecordFormValues> = {
  notes: "",
  taken: 1,
};

// Helper function to calculate working days between two dates (excluding weekends)
const calculateWorkingDays = (startDate: Date, endDate: Date): number => {
  let count = 0;
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.getDay();
    // Skip weekends (0 is Sunday, 6 is Saturday)
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      count++;
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return count;
};

export function LeaveManagement() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: session } = useSession();
  
  // Get employerId from session
  const employerId = session?.user?.employerId;
  
  // Show loading while session is loading
  if (!session) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="mr-2">טוען...</span>
        </CardContent>
      </Card>
    );
  }
  
  // Show error if no employerId
  if (!employerId) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-6">
          <p className="text-center text-red-600">שגיאה: לא נמצא מזהה מעסיק בהפעלה</p>
        </CardContent>
      </Card>
    );
  }
  
  // Get employees for dropdown
  const { data: employeesData, isLoading: isLoadingEmployees } = api.employer.getEmployees.useQuery(
    { employerId: employerId as string },
    { enabled: !!employerId }
  );
  
  const employees = employeesData?.employees;
  
  // Get leave balances for selected employee
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null);
  const { data: leaveBalances, isLoading: isLoadingBalances } = api.leaveRecord.getBalances.useQuery(
    { employeeId: selectedEmployeeId as string },
    { enabled: !!selectedEmployeeId }
  );
  
  // Create leave record mutation
  const createLeaveMutation = api.leaveRecord.create.useMutation({
    onSuccess: () => {
      toast.success("רשומת החופשה נשמרה בהצלחה");
      form.reset(defaultValues);
    },
    onError: (error) => {
      toast.error(`שגיאה בשמירת רשומת החופשה: ${error.message}`);
    },
  });
  
  // Form definition
  const form = useForm<LeaveRecordFormValues>({
    resolver: zodResolver(leaveRecordSchema),
    defaultValues,
  });
  
  // Watch form values to update calculations
  const startDate = form.watch("startDate");
  const endDate = form.watch("endDate");
  const leaveType = form.watch("leaveType");
  
  // Auto-calculate days taken when dates change
  React.useEffect(() => {
    if (startDate && endDate) {
      const days = calculateWorkingDays(startDate, endDate);
      form.setValue("taken", days);
    }
  }, [startDate, endDate, form]);
  
  // Update selected employee when form value changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "employeeId" && value.employeeId) {
        setSelectedEmployeeId(value.employeeId as string);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);
  
  // Form submission handler
  const onSubmit = async (data: LeaveRecordFormValues) => {
    setIsSubmitting(true);
    try {
      // Convert to API format and add current year/month
      const now = new Date();
      const apiData = {
        ...data,
        year: now.getFullYear(),
        month: now.getMonth() + 1,
      };
      
      await createLeaveMutation.mutateAsync(apiData);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Helper to get the current balance for leave type
  const getBalance = (type: LeaveType) => {
    if (!leaveBalances) return null;
    return leaveBalances.find((balance: LeaveBalance) => balance.leaveType === type);
  };
  
  // Get relevant balance
  const currentBalance = leaveType ? getBalance(leaveType) : null;
  
  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>רישום חופשה / מחלה / היעדרות</CardTitle>
        <CardDescription>
          רישום חופשות, מחלות והיעדרויות לצורך חישוב בתלוש השכר
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="employeeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>עובד</FormLabel>
                  <Select
                    disabled={isLoadingEmployees}
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="בחר עובד" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {employees?.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.fullName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="leaveType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>סוג היעדרות</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="בחר סוג היעדרות" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="VACATION">חופשה</SelectItem>
                      <SelectItem value="SICK">מחלה</SelectItem>
                      <SelectItem value="UNPAID">חופשה ללא תשלום</SelectItem>
                      <SelectItem value="MATERNITY">חופשת לידה</SelectItem>
                      <SelectItem value="MILITARY">מילואים</SelectItem>
                      <SelectItem value="INTER_VISA">בין אשרות</SelectItem>
                      <SelectItem value="OTHER">אחר</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {currentBalance && (
              <div className="p-3 border rounded-md bg-muted/50">
                <p className="text-sm font-medium">
                  יתרה נוכחית: {currentBalance.entitlement} ימים
                </p>
                {currentBalance.taken > 0 && (
                  <p className="text-sm">נוצלו: {currentBalance.taken} ימים</p>
                )}
                <p className="text-sm font-semibold mt-1">
                  יתרה זמינה: {(currentBalance.entitlement || 0) - (currentBalance.taken || 0)} ימים
                </p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>תאריך התחלה</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-right font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "dd/MM/yyyy")
                            ) : (
                              <span>בחר תאריך</span>
                            )}
                            <CalendarIcon className="mr-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>תאריך סיום</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-right font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "dd/MM/yyyy")
                            ) : (
                              <span>בחר תאריך</span>
                            )}
                            <CalendarIcon className="mr-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="taken"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>מספר ימים</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.5" {...field} />
                  </FormControl>
                  <FormDescription>
                    מספר ימי ההיעדרות (כולל חצאי ימים)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>הערות</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="הערות נוספות לגבי ההיעדרות"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
      
      <CardFooter className="flex justify-end">
        <Button 
          onClick={form.handleSubmit(onSubmit)} 
          disabled={isSubmitting || isLoadingEmployees}
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isSubmitting ? "שומר..." : "שמור היעדרות"}
        </Button>
      </CardFooter>
    </Card>
  );
} 