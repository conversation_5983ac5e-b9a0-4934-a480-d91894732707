import { DeductionType, TaxCalculationType, SocialSecurityCalculationType, DeductionGroup } from "@prisma/client";

// Re-export Prisma types for use in other components
export { DeductionType, TaxCalculationType, SocialSecurityCalculationType, DeductionGroup };

// ============================================
// Deduction Component Types
// ============================================

export interface DeductionComponent {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  deductionType: DeductionType;
  taxCalculation: TaxCalculationType;
  socialSecurityCalculation: SocialSecurityCalculationType;
  affectsPension: boolean;
  percentageOfSalary?: number | null;
  rewardCode?: string | null;
  displayOrder?: number;
  group?: string | null;
  isOneTime: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

// Enums for deduction types
// Removed local DeductionType enum
// Removed local TaxCalculationType enum
// Removed local SocialSecurityCalculationType enum
// Removed local DeductionGroup enum - now using Prisma client enum

// ============================================
// Form Types
// ============================================

export interface DeductionComponentFormData extends Omit<DeductionComponent, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> {
  id?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  deletedAt?: Date | string | null;
  code: string;
  name: string;
  description?: string;
  deductionType: DeductionType;
  taxCalculation: TaxCalculationType;
  socialSecurityCalculation: SocialSecurityCalculationType;
  affectsPension: boolean;
  percentageOfSalary?: number;
  rewardCode?: string;
  displayOrder: number;
  group?: string;
  isOneTime: boolean;
  isActive: boolean;
}

// ============================================
// Filter and Search Types
// ============================================

export interface DeductionComponentFilters {
  search?: string;
  deductionType?: DeductionType;
  group?: DeductionGroup;
  isActive?: boolean;
  isOneTime?: boolean;
}

// ============================================
// Report Types
// ============================================

export interface DeductionChangeLog {
  id: string;
  deductionComponentId: string;
  deductionComponent?: DeductionComponent;
  changeType: "CREATE" | "UPDATE" | "DELETE";
  changeDetails: Record<string, any>;
  changedBy: string;
  changedAt: Date;
}

export interface DeductionChangeReport {
  fromDate: Date;
  toDate: Date;
  changes: DeductionChangeLog[];
  summary: {
    totalChanges: number;
    createdCount: number;
    updatedCount: number;
    deletedCount: number;
  };
}

// ============================================
// Usage Types
// ============================================

export interface DeductionUsage {
  deductionComponentId: string;
  deductionComponent?: DeductionComponent;
  usageCount: number;
  activeEmployees: number;
  totalAmount: number;
  lastUsed?: Date;
}

// ============================================
// Validation Types
// ============================================

export interface DeductionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ============================================
// Constants
// ============================================

export const DEDUCTION_TYPE_LABELS: Record<DeductionType, string> = {
  [DeductionType.INCOME_TAX]: "מס הכנסה",
  [DeductionType.NATIONAL_INSURANCE]: "ביטוח לאומי",
  [DeductionType.HEALTH_INSURANCE]: "ביטוח בריאות",
  [DeductionType.PENSION]: "פנסיה",
  [DeductionType.EDUCATION_FUND]: "קרן השתלמות",
  [DeductionType.LOAN_REPAYMENT]: "החזר הלוואה",
  [DeductionType.OTHER]: "אחר",
};

export const TAX_CALCULATION_LABELS: Record<TaxCalculationType, string> = {
  [TaxCalculationType.TAX_LIABLE]: "חייב מ\"ה",
  [TaxCalculationType.TAX_EXEMPT]: "פטור מ\"ה",
};

export const SOCIAL_SECURITY_LABELS: Record<SocialSecurityCalculationType, string> = {
  [SocialSecurityCalculationType.SS_LIABLE]: "חייב ב\"ל",
  [SocialSecurityCalculationType.SS_EXEMPT]: "פטור ב\"ל",
};

export const DEDUCTION_GROUP_LABELS: Record<DeductionGroup, string> = {
  [DeductionGroup.MANDATORY]: "ניכויי חובה",
  [DeductionGroup.VOLUNTARY]: "ניכויים וולונטריים",
  [DeductionGroup.OTHER]: "אחר",
};