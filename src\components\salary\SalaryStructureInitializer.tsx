import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2 } from "lucide-react";
import { api } from "../../utils/api";
import { toast } from "sonner";

/**
 * This component provides a UI for initializing a complete salary structure
 * with interconnected components like payment components, deduction components,
 * templates, agreements, and associations.
 */
export function SalaryStructureInitializer() {
  const [isInitializing, setIsInitializing] = useState(false);
  const [includeOvertime, setIncludeOvertime] = useState(true);
  const [includeSickLeave, setIncludeSickLeave] = useState(true);
  const [includeHolidays, setIncludeHolidays] = useState(true);
  const [includeAssociations, setIncludeAssociations] = useState(true);
  
  // Add API hooks for the initialization endpoints
  const createBasicComponentsMutation = api.salarySetup.createBasicComponents.useMutation({
    onSuccess: () => {
      toast.success("בסיס רכיבי שכר נוצר בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה ביצירת רכיבי שכר: ${error.message}`);
    }
  });

  const createTemplatesMutation = api.salarySetup.createTemplates.useMutation({
    onSuccess: () => {
      toast.success("תבניות שכר נוצרו בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה ביצירת תבניות שכר: ${error.message}`);
    }
  });

  const createAgreementsMutation = api.salarySetup.createAgreements.useMutation({
    onSuccess: () => {
      toast.success("הסכמי שכר נוצרו בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה ביצירת הסכמי שכר: ${error.message}`);
    }
  });

  const createAssociationsMutation = api.salarySetup.createAssociations.useMutation({
    onSuccess: () => {
      toast.success("שיוכים נוצרו בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה ביצירת שיוכים: ${error.message}`);
    }
  });

  const handleInitialize = async () => {
    setIsInitializing(true);
    try {
      // Step 1: Create basic payment and deduction components
      await createBasicComponentsMutation.mutateAsync({
        includeOvertime,
        includeSickLeave,
        includeHolidays,
      });
      
      // Step 2: Create salary templates
      await createTemplatesMutation.mutateAsync({
        includeOvertime,
        includeSickLeave,
        includeHolidays,
      });
      
      // Step 3: Create salary and attendance agreements
      await createAgreementsMutation.mutateAsync({
        includeOvertime,
        includeSickLeave,
        includeHolidays,
      });
      
      // Step 4: Create associations if requested
      if (includeAssociations) {
        await createAssociationsMutation.mutateAsync({});
      }
      
      toast.success("מערכת השכר אותחלה בהצלחה!");
    } catch (error) {
      toast.error("אירעה שגיאה באתחול מערכת השכר");
      console.error(error);
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>אתחול מבנה שכר</CardTitle>
        <CardDescription>
          הגדרה של רכיבי שכר, ניכויים, תבניות, הסכמים ושיוכים בצורה מסונכרנת
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="components">
          <TabsList className="mb-4">
            <TabsTrigger value="components">רכיבי שכר</TabsTrigger>
            <TabsTrigger value="templates">תבניות שכר</TabsTrigger>
            <TabsTrigger value="agreements">הסכמים</TabsTrigger>
            <TabsTrigger value="associations">שיוכים</TabsTrigger>
          </TabsList>
          
          <TabsContent value="components">
            <div className="grid gap-4">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="includeOvertime"
                  checked={includeOvertime}
                  onCheckedChange={(checked) => setIncludeOvertime(!!checked)}
                />
                <Label htmlFor="includeOvertime">רכיבי שעות נוספות</Label>
              </div>
              
              <div className="flex items-center gap-2">
                <Checkbox
                  id="includeSickLeave"
                  checked={includeSickLeave}
                  onCheckedChange={(checked) => setIncludeSickLeave(!!checked)}
                />
                <Label htmlFor="includeSickLeave">רכיבי ימי מחלה</Label>
              </div>
              
              <div className="flex items-center gap-2">
                <Checkbox
                  id="includeHolidays"
                  checked={includeHolidays}
                  onCheckedChange={(checked) => setIncludeHolidays(!!checked)}
                />
                <Label htmlFor="includeHolidays">רכיבי חגים וחופשות</Label>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="templates">
            <p className="text-sm text-gray-500 mb-4">
              תבניות שכר יהיו מסונכרנות עם רכיבי השכר הנבחרים ויכללו את הקטגוריות הבאות:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>תבנית שכר בסיסית</li>
              <li>תבנית שכר למנהלים</li>
              <li>תבנית שכר לעובדים זרים</li>
              <li>תבנית שכר עם הפרשות פנסיוניות מורחבות</li>
            </ul>
          </TabsContent>
          
          <TabsContent value="agreements">
            <p className="text-sm text-gray-500 mb-4">
              הסכמי שכר ונוכחות יכללו את הסוגים הבאים:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>הסכם נוכחות סטנדרטי (משרה מלאה, 42 שעות שבועיות)</li>
              <li>הסכם נוכחות לעובדי בניין (משרה מלאה, 45 שעות שבועיות)</li>
              <li>הסכם נוכחות למשרה חלקית</li>
              <li>הסכם שכר כולל זכאויות חופשה, מחלה והבראה לפי חוק</li>
            </ul>
          </TabsContent>
          
          <TabsContent value="associations">
            <div className="mb-4">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="includeAssociations"
                  checked={includeAssociations}
                  onCheckedChange={(checked) => setIncludeAssociations(!!checked)}
                />
                <Label htmlFor="includeAssociations">יצירת שיוכים אוטומטית</Label>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                יצירת שיוכים אוטומטית תחבר בין העובדים לתבניות, הסכמים, מחלקות ותפקידים
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="flex justify-end">
        <Button 
          onClick={handleInitialize} 
          disabled={isInitializing}
        >
          {isInitializing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isInitializing ? "מאתחל..." : "אתחל מבנה שכר"}
        </Button>
      </CardFooter>
    </Card>
  );
} 