import { describe, expect, it, vi } from "vitest";
process.env.SKIP_ENV_VALIDATION = "true";
vi.mock("@/server/auth", () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import("../../trpc");
const { movementRouter } = await import("../movement");
const createCaller = createCallerFactory(movementRouter);

const baseCtx = {
	db: {},
	logger: undefined,
	headers: new Headers(),
} as unknown as Record<string, unknown>;

describe("movementRouter.validate", () => {
	it("detects missing amount", async () => {
		const dbMock = {
			salaryTransaction: { findFirst: vi.fn().mockResolvedValue(null) },
		} as unknown as Record<string, unknown>;
		const caller = createCaller({
			...baseCtx,
			db: dbMock,
			session: { user: { id: "u1" } },
		});
		const res = await caller.validate({
			employeeId: "e1",
			periodMonth: 5,
			periodYear: 2024,
		});
		expect(res.isValid).toBe(false);
		expect(res.errors).toContain("נדרש סכום או שיעור");
	});

	it("detects overlap", async () => {
		const dbMock = {
			salaryTransaction: { findFirst: vi.fn().mockResolvedValue({ id: "t1" }) },
		} as unknown as Record<string, unknown>;
		const caller = createCaller({
			...baseCtx,
			db: dbMock,
			session: { user: { id: "u1" } },
		});
		const res = await caller.validate({
			employeeId: "e1",
			periodMonth: 5,
			periodYear: 2024,
			amount: 100,
			fromDate: new Date("2024-05-01"),
			toDate: new Date("2024-05-05"),
		});
		expect(res.isValid).toBe(false);
		expect(res.errors).toContain("קיימת תנועה חופפת");
	});
});
