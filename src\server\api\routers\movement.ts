import {
	createTRPCRouter,
	protectedProcedure,
	publicProcedure,
} from "@/server/api/trpc";
import { PayslipItemKod, type Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

const baseSchema = z.object({
	employeeId: z.string(),
	periodMonth: z.number().min(1).max(12),
	periodYear: z.number().min(1900),
	componentCode: z.nativeEnum(PayslipItemKod).optional(),
	description: z.string().optional(),
	quantity: z.number().optional(),
	rate: z.number().optional(),
	percentage: z.number().optional(),
	amount: z.number().optional(),
	fromDate: z.coerce.date().optional(),
	toDate: z.coerce.date().optional(),
	departmentId: z.string().optional(),
	note: z.string().optional(),
});

const createSchema = baseSchema;
const updateSchema = baseSchema.extend({ id: z.string() });

function csvToObjects(csv: string): unknown[] {
	const lines = csv.trim().split(/\r?\n/);
	if (lines.length < 2) return [];
	const headers =
		lines
			.shift()
			?.split(",")
			.map((h) => h.trim()) ?? [];
	return lines.map((line) => {
		const values = line.split(",").map((v) => v.trim());
		const row: Record<string, string> = {};
		headers.forEach((h, i) => {
			row[h] = values[i] || "";
		});
		return row;
	});
}

async function findOverlap(
	db: Prisma.Client,
	input: {
		employeeId: string;
		componentCode?: PayslipItemKod | null;
		fromDate?: Date | null;
		toDate?: Date | null;
		id?: string;
	},
) {
	if (!input.fromDate || !input.toDate) return null;
	return db.salaryTransaction.findFirst({
		where: {
			employeeId: input.employeeId,
			componentCode: input.componentCode || undefined,
			id: input.id ? { not: input.id } : undefined,
			fromDate: { lte: input.toDate },
			toDate: { gte: input.fromDate },
		},
	});
}

export const movementRouter = createTRPCRouter({
	getAll: protectedProcedure
		.input(
			z
				.object({
					employeeId: z.string().optional(),
					year: z.number().optional(),
					month: z.number().optional(),
				})
				.optional(),
		)
		.query(async ({ ctx, input }) => {
			const where: Prisma.SalaryTransactionWhereInput = {};
			if (input?.employeeId) where.employeeId = input.employeeId;
			if (input?.year) where.periodYear = input.year;
			if (input?.month) where.periodMonth = input.month;
			return ctx.db.salaryTransaction.findMany({
				where,
				orderBy: { createdAt: "desc" },
			});
		}),

	getById: protectedProcedure
		.input(z.string())
		.query(async ({ ctx, input }) => {
			const tx = await ctx.db.salaryTransaction.findUnique({
				where: { id: input },
			});
			if (!tx)
				throw new TRPCError({ code: "NOT_FOUND", message: "תנועה לא נמצאה" });
			return tx;
		}),

	create: protectedProcedure
		.input(createSchema)
		.mutation(async ({ ctx, input }) => {
			const overlap = await findOverlap(ctx.db, input);
			if (overlap) {
				throw new TRPCError({ code: "CONFLICT", message: "קיימת תנועה חופפת" });
			}
			if (!input.amount && !input.rate && !input.percentage) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "נדרש סכום או שיעור",
				});
			}
			const tenantId = "default";
			return ctx.db.salaryTransaction.create({ data: { ...input, tenantId } });
		}),

	update: protectedProcedure
		.input(updateSchema)
		.mutation(async ({ ctx, input }) => {
			const { id, ...data } = input;
			const existing = await ctx.db.salaryTransaction.findUnique({
				where: { id },
			});
			if (!existing)
				throw new TRPCError({ code: "NOT_FOUND", message: "תנועה לא נמצאה" });
			const overlap = await findOverlap(ctx.db, { ...data, id });
			if (overlap) {
				throw new TRPCError({ code: "CONFLICT", message: "קיימת תנועה חופפת" });
			}
			if (!data.amount && !data.rate && !data.percentage) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "נדרש סכום או שיעור",
				});
			}
			return ctx.db.salaryTransaction.update({ where: { id }, data });
		}),

	delete: protectedProcedure
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			await ctx.db.salaryTransaction.delete({ where: { id: input } });
			return { success: true };
		}),

	importCsv: protectedProcedure
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			const rows = csvToObjects(input);
			let imported = 0;
			for (const raw of rows) {
				const parsed = createSchema.safeParse({
					...raw,
					periodMonth: raw.periodMonth ? Number(raw.periodMonth) : undefined,
					periodYear: raw.periodYear ? Number(raw.periodYear) : undefined,
					quantity: raw.quantity ? Number(raw.quantity) : undefined,
					rate: raw.rate ? Number(raw.rate) : undefined,
					percentage: raw.percentage ? Number(raw.percentage) : undefined,
					amount: raw.amount ? Number(raw.amount) : undefined,
					fromDate: raw.fromDate ? new Date(raw.fromDate) : undefined,
					toDate: raw.toDate ? new Date(raw.toDate) : undefined,
				});
				if (!parsed.success) continue;
				const overlap = await findOverlap(ctx.db, parsed.data);
				if (overlap) continue;
				const tenantId = "default";
				await ctx.db.salaryTransaction.create({
					data: { ...parsed.data, tenantId },
				});
				imported++;
			}
			return { imported, total: rows.length };
		}),

	importExcel: protectedProcedure.input(z.string()).mutation(async () => {
		// TODO: implement Excel parsing
		return { imported: 0, total: 0 };
	}),

	validate: protectedProcedure
		.input(createSchema)
		.query(async ({ ctx, input }) => {
			const errors: string[] = [];
			if (!input.amount && !input.rate && !input.percentage) {
				errors.push("נדרש סכום או שיעור");
			}
			if (
				(input.fromDate && !input.toDate) ||
				(!input.fromDate && input.toDate)
			) {
				errors.push("יש לציין טווח תאריכים מלא");
			}
			const overlap = await findOverlap(ctx.db, input);
			if (overlap) errors.push("קיימת תנועה חופפת");
			return { isValid: errors.length === 0, errors };
		}),
});
