"use client";

import { api } from "@/trpc/react";
import { toast } from "sonner";
import type { SalaryTemplate, SalaryTemplateFormData } from "./types";

export function useSalaryTemplates(employerId?: string) {
  return api.salaryTemplate.list.useQuery({ employerId });
}

export function useSalaryTemplate(id: string) {
  return api.salaryTemplate.getById.useQuery({ id }, { enabled: !!id });
}

export function useCreateSalaryTemplate() {
    const utils = api.useUtils();
  return api.salaryTemplate.create.useMutation({
    onSuccess: () => {
      utils.salaryTemplate.list.invalidate();
      toast.success("תבנית נוצרה בהצלחה");
    },
    onError: (e) => toast.error(e.message),
  });
}

export function useUpdateSalaryTemplate() {
    const utils = api.useUtils();
  return api.salaryTemplate.update.useMutation({
    onSuccess: (data) => {
      utils.salaryTemplate.list.invalidate();
      utils.salaryTemplate.getById.invalidate({ id: data.id });
      toast.success("תבנית עודכנה");
    },
    onError: (e) => toast.error(e.message),
  });
}

export function useDeleteSalaryTemplate() {
    const utils = api.useUtils();
  return api.salaryTemplate.delete.useMutation({
    onSuccess: () => {
      utils.salaryTemplate.list.invalidate();
      toast.success("תבנית נמחקה");
    },
    onError: (e) => toast.error(e.message),
  });
}

export function useDuplicateSalaryTemplate() {
    const utils = api.useUtils();
  return api.salaryTemplate.duplicate.useMutation({
    onSuccess: () => {
      utils.salaryTemplate.list.invalidate();
      toast.success("תבנית שוכפלה");
    },
    onError: (e) => toast.error(e.message),
  });
}
