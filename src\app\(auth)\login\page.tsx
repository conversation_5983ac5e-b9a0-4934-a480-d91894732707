"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";

// Define form schema with Zod for validation
const formSchema = z.object({
  email: z.string().email({ message: "כתובת אימייל לא תקינה" }),
  password: z.string().min(6, { message: "סיסמה חייבת להכיל לפחות 6 תווים" }),
  rememberMe: z.boolean().optional(),
});

// Type for our form values
type FormValues = z.infer<typeof formSchema>;

export default function LoginPage() {
  const router = useRouter();
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with react-hook-form and zod resolver
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);
    setError("");
    console.log("onSubmit", data);
    try {
      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });
      console.log("result", result);
        if(result?.status === 200){
          console.log("pushing to dashboard");

          router.push("/employer-dashboard");
        }
      if (result?.error) {
        setError("אימייל או סיסמה לא תקינים");
        setIsLoading(false);
      } else {
        router.push("/employer-dashboard");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("אירעה שגיאה בלתי צפויה");
      setIsLoading(false);
    }
  };

  // הפונקציות המעודכנות - התחברות ישירה
  const loginAsOwner = async () => {
    setIsLoading(true);
    setError("");
    try {
      const result = await signIn("credentials", {
        email: "<EMAIL>",
        password: "123456789",
        redirect: false,
      });
      if (result?.error) {
        setError("אימייל או סיסמה לא תקינים");
        setIsLoading(false);
      } else {
        router.push("/employer-dashboard");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("אירעה שגיאה בלתי צפויה");
      setIsLoading(false);
    }
  };

  const loginAsAdmin = async () => {
    setIsLoading(true);
    setError("");
    try {
      const result = await signIn("credentials", {
        email: "<EMAIL>",
        password: "123456789",
        redirect: false,
      });
      if (result?.error) {
        setError("אימייל או סיסמה לא תקינים");
        setIsLoading(false);
      } else {
        router.push("/employer-dashboard");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("אירעה שגיאה בלתי צפויה");
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left column - decorative */}
      <div className="hidden lg:flex lg:w-1/2 flex-col items-center justify-center p-12 relative overflow-hidden bg-gradient-to-br from-indigo-900 via-purple-800 to-violet-900 animate-gradient-x">
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full">
            {/* Animated floating particles */}
            <div className="absolute w-20 h-20 rounded-full bg-white/5 animate-float-slow top-1/4 left-1/4" />
            <div className="absolute w-12 h-12 rounded-full bg-white/10 animate-float-medium top-3/4 left-1/3" />
            <div className="absolute w-16 h-16 rounded-full bg-white/5 animate-float-fast top-1/2 left-2/3" />
            <div className="absolute w-24 h-24 rounded-full bg-white/5 animate-float-slow-reverse top-1/5 left-1/2" />
            <div className="absolute w-10 h-10 rounded-full bg-white/10 animate-float-medium-reverse top-2/3 left-1/5" />

            {/* Light beams */}
            <div className="absolute top-0 left-1/4 w-1/2 h-full bg-gradient-radial from-purple-500/20 to-transparent opacity-60 blur-3xl" />
            <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-gradient-radial from-indigo-500/20 to-transparent opacity-60 blur-3xl" />
          </div>
        </div>
        <div className="absolute inset-0 bg-black opacity-10 z-0" />
        <div className="z-10 max-w-xl text-white">
          <h1 className="text-5xl font-bold mb-8 text-right">ברוכים הבאים למערכת השכר</h1>
          <p className="text-xl mb-8 text-right leading-relaxed">
            נהל את משכורות העובדים שלך בקלות ויעילות עם הפלטפורמה המתקדמת ביותר.
          </p>
          <div className="grid grid-cols-2 gap-6 mt-12">
            <div className="backdrop-blur-sm bg-white/10 p-6 rounded-xl">
              <div className="text-4xl mb-2">+250</div>
              <div className="text-sm opacity-80">לקוחות מרוצים</div>
            </div>
            <div className="backdrop-blur-sm bg-white/10 p-6 rounded-xl">
              <div className="text-4xl mb-2">99.9%</div>
              <div className="text-sm opacity-80">זמן פעילות</div>
            </div>
          </div>
        </div>

        {/* Decorative circles */}
        <div className="absolute -bottom-24 -left-24 w-80 h-80 rounded-full bg-white/10 backdrop-blur-sm" />
        <div className="absolute top-1/4 -right-20 w-60 h-60 rounded-full bg-white/5 backdrop-blur-sm" />
      </div>

      {/* Right column - login form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center bg-white dark:bg-gray-900 p-4">
        <div className="w-full max-w-md space-y-8 p-8">
          <div className="text-center">
            <div className="inline-block relative mb-4">
              <div className="w-16 h-16 bg-gradient-to-tr from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="absolute -bottom-2 -right-2 w-full h-full bg-indigo-200 rounded-xl -z-10 opacity-50" />
            </div>

            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              התחברות למערכת
            </h2>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              הזן את פרטי הכניסה שלך להמשך
            </p>
          </div>

          {error && (
            <div className="flex items-center p-4 mb-4 rounded-lg bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 ml-3" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div className="text-sm text-red-700 dark:text-red-200">{error}</div>
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-right block">אימייל</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input
                          placeholder="כתובת אימייל"
                          {...field}
                          dir="rtl"
                          className="text-right px-4 py-3 pr-12"
                          suppressHydrationWarning
                        />
                      </FormControl>
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                      </div>
                    </div>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-right block">סיסמה</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="סיסמה"
                          {...field}
                          dir="rtl"
                          className="text-right px-4 py-3 pr-12"
                          suppressHydrationWarning
                        />
                      </FormControl>
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <div className="flex items-center justify-between">
                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <div className="flex items-center space-x-2 space-x-reverse rtl:space-x-reverse">
                      <Checkbox
                        id="rememberMe"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        suppressHydrationWarning
                      />
                      <label
                        htmlFor="rememberMe"
                        className="text-sm text-gray-900 dark:text-gray-300"
                      >
                        זכור אותי
                      </label>
                    </div>
                  )}
                />
                <div className="text-sm">
                  <a href="#" className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300">
                    שכחת סיסמה?
                  </a>
                </div>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full justify-center py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-md transition-all transform hover:scale-[1.02] disabled:opacity-70 disabled:hover:scale-100"
                suppressHydrationWarning
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    מתחבר...
                  </div>
                ) : (
                  "התחבר"
                )}
              </Button>

              <div className="flex items-center justify-center mt-4">
                <span className="text-gray-500 dark:text-gray-400">אין לך חשבון? </span>
                <a href="#" className="text-indigo-600 hover:text-indigo-500 mr-1 font-medium dark:text-indigo-400 dark:hover:text-indigo-300">הירשם עכשיו</a>
              </div>

              {/* Development auto-fill section */}
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-sm font-semibold text-center text-gray-500 dark:text-gray-400 mb-4">כניסה מהירה למצב פיתוח</h3>
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    type="button"
                    onClick={loginAsOwner}
                    variant="outline"
                    className="text-sm py-2 h-auto"
                    disabled={isLoading}
                  >
                    בעל המערכת / מנהל
                  </Button>
                  <Button
                    type="button"
                    onClick={loginAsAdmin}
                    variant="outline"
                    className="text-sm py-2 h-auto"
                    disabled={isLoading}
                  >
                    מנהל שכר
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}