import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import type { Metada<PERSON> } from "next";
import { AgreementDefinitionTab } from "./components/AgreementDefinitionTab";

export const metadata: Metadata = {
  title: "הסכמי נוכחות - מערכת שכר",
  description: "ניהול הסכמי נוכחות במערכת",
};

export default function AttendanceAgreementsPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">הסכמי נוכחות</h1>
        <p className="text-muted-foreground">
          הגדר את הסכמי הנוכחות לעובדים במערכת
        </p>
      </div>

      <Tabs defaultValue="agreements" className="space-y-4">
        <TabsList>
          <TabsTrigger value="agreements">הגדרת הסכמים</TabsTrigger>
        </TabsList>
        <TabsContent value="agreements" className="space-y-4">
          <AgreementDefinitionTab />
        </TabsContent>
      </Tabs>
    </div>
  );
} 