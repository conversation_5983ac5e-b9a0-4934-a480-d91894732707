---
description: Comprehensive development guidelines for the salary management T3 stack project
globs: src/**/*.{ts,tsx}, prisma/**/*.prisma, *.{js,json,md}
alwaysApply: true
---

# Salary Management System - Development Guidelines

## **Project Overview**
- **Stack**: T3 (Next.js 15, TypeScript, tRPC, Prisma, Tailwind CSS)
- **UI Library**: shadcn/ui with Radix UI primitives
- **State Management**: React Query via tRPC
- **Database**: PostgreSQL with Prisma ORM
- **Language**: Hebrew UI, English code
- **React Version**: React 19.1.0

## **Code Structure Guidelines**

### **File Organization**
- **Components**: One component per file in `src/components/` or feature-specific `components/` folders
- **Types**: Separate `types.ts` files for each feature
- **Hooks**: Custom hooks in `hooks.ts` files
- **API Routes**: tRPC routers in `src/server/api/routers/`
- **Utilities**: Helper functions in `src/lib/`

### **Naming Conventions**
- **Components**: PascalCase (e.g., `EmployeeTable.tsx`)
- **Functions/Hooks**: camelCase (e.g., `useEmployees`, `calculateSalary`)
- **Types/Interfaces**: PascalCase (e.g., `Employee`, `SalaryCalculation`)
- **Files**: kebab-case (e.g., `employee-form.tsx`)
- **tRPC Procedures**: camelCase (e.g., `getAll`, `createEmployee`)

## **TypeScript Best Practices**

### **Type Safety**
- **Always define explicit types** - avoid `any`
- **Use Zod schemas** for runtime validation
- **Infer types from Zod** when possible
  ```typescript
  const employeeSchema = z.object({
    id: z.string(),
    name: z.string(),
    email: z.string().email()
  });
  
  type Employee = z.infer<typeof employeeSchema>;
  ```

### **Type Organization**
- **Group related types** in feature-specific `types.ts`
- **Export all types** for reusability
- **Use discriminated unions** for state management
  ```typescript
  type LoadingState = 
    | { status: 'idle' }
    | { status: 'loading' }
    | { status: 'success'; data: Employee[] }
    | { status: 'error'; error: string };
  ```

## **tRPC Implementation**

### **Router Structure**
- **One router per domain** (e.g., `employee.ts`, `salary.ts`)
- **Consistent procedure naming**: `getAll`, `getById`, `create`, `update`, `delete`
- **Input validation with Zod**
  ```typescript
  export const employeeRouter = createTRPCRouter({
    getAll: publicProcedure
      .input(z.object({
        status: z.enum(['ACTIVE', 'INACTIVE']).optional()
      }))
      .query(async ({ ctx, input }) => {
        // Implementation
      })
  });
  ```

### **Client Usage**
- **Use custom hooks** to wrap tRPC calls
- **Handle loading and error states**
- **Implement optimistic updates**
  ```typescript
  export function useCreateEmployee() {
      const utils = api.useUtils();
    
    return api.employee.create.useMutation({
      onSuccess: () => {
        utils.employee.getAll.invalidate();
        toast.success("עובד נוסף בהצלחה");
      },
      onError: (error) => {
        toast.error(`שגיאה: ${error.message}`);
      }
    });
  }
  ```

## **React Component Guidelines**

### **Component Structure**
- **Props interface** at the top
- **Custom hooks** for logic separation
- **Early returns** for loading/error states
- **Semantic HTML** with proper ARIA labels
  ```typescript
  interface EmployeeTableProps {
    departmentId?: string;
    onEmployeeSelect?: (employee: Employee) => void;
  }
  
  export function EmployeeTable({ departmentId, onEmployeeSelect }: EmployeeTableProps) {
    const { data, isLoading, error } = useEmployees({ departmentId });
    
    if (isLoading) return <LoadingSpinner />;
    if (error) return <ErrorMessage error={error} />;
    if (!data?.length) return <EmptyState />;
    
    return (
      <Table>
        {/* Table implementation */}
      </Table>
    );
  }
  ```

### **State Management**
- **Prefer server state** via tRPC over local state
- **Use controlled components** for forms
- **Implement proper cleanup** in useEffect

## **UI/UX Guidelines**

### **Hebrew Language Support**
- **RTL Layout**: Use `dir="rtl"` on root elements
- **Hebrew Labels**: All UI text in Hebrew
- **Date Format**: DD/MM/YYYY
- **Number Format**: Use Hebrew numerals where appropriate

### **Accessibility**
- **ARIA Labels**: Hebrew descriptions for screen readers
- **Keyboard Navigation**: Support Tab, Enter, Escape
- **Focus Management**: Proper focus trapping in modals
- **Color Contrast**: WCAG AA compliance

### **Error Handling**
- **User-friendly messages** in Hebrew
- **Toast notifications** for actions
- **Form validation** with inline errors
- **Loading states** for all async operations

## **Database Schema Guidelines**

### **Prisma Models**
- **Consistent naming**: PascalCase for models, camelCase for fields
- **Soft deletes**: Use `deletedAt` field instead of hard deletes
- **Timestamps**: Always include `createdAt` and `updatedAt`
- **Relations**: Define both sides of relationships
  ```prisma
  model Employee {
    id        String   @id @default(cuid())
    name      String
    email     String   @unique
    status    Status   @default(ACTIVE)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
    deletedAt DateTime?
    
    associations Association[]
  }
  ```

## **Testing Guidelines**

### **Unit Tests**
- **Test utilities and helpers**
- **Mock tRPC procedures**
- **Test Zod schemas**

### **Integration Tests**
- **Test API routes**
- **Test database operations**
- **Test form submissions**

## **Performance Optimization**

### **Query Optimization**
- **Use proper indexes** in database
- **Implement pagination** for large datasets
- **Use `select` to limit fields**
- **Cache frequently accessed data**

### **React Optimization**
- **Memoize expensive computations**
- **Use React.memo for pure components**
- **Implement virtual scrolling for long lists**
- **Lazy load heavy components**

## **Security Best Practices**

### **Data Validation**
- **Validate all inputs** with Zod
- **Sanitize user input**
- **Use parameterized queries**
- **Implement rate limiting**

### **Authentication & Authorization**
- **Use NextAuth.js** for authentication
- **Implement role-based access**
- **Validate permissions server-side**
- **Secure API endpoints**

## **Development Workflow**

### **Git Conventions**
- **Branch naming**: `feature/description`, `fix/description`
- **Commit messages**: Hebrew description of changes
- **Pull requests**: Include testing checklist

### **Code Review Checklist**
- **Type safety**: No `any` types
- **Error handling**: All errors caught
- **Hebrew UI**: All text translated
- **Accessibility**: ARIA labels present
- **Performance**: No unnecessary re-renders

## **Common Patterns**

### **CRUD Operations**
```typescript
// hooks.ts
export const useEmployees = () => api.employee.getAll.useQuery();
export const useEmployee = (id: string) => api.employee.getById.useQuery({ id });
export const useCreateEmployee = () => {
    const utils = api.useUtils();
  return api.employee.create.useMutation({
    onSuccess: () => utils.employee.invalidate()
  });
};
```

### **Form Handling**
```typescript
// Use React Hook Form with Zod
const form = useForm<EmployeeFormData>({
  resolver: zodResolver(employeeSchema),
  defaultValues: initialData
});
```

### **Modal Management**
```typescript
// Use controlled state
const [isOpen, setIsOpen] = useState(false);
const [selectedItem, setSelectedItem] = useState<Employee | null>(null);
```

## **Documentation Requirements**

### **Code Comments**
- **Hebrew comments** for business logic
- **JSDoc** for complex functions
- **TODO comments** with assignee and date

### **Feature Documentation**
- **Create `.cursorrules`** for each major feature
- **Update `PROJECT_PROGRESS.md`** regularly
- **Document API changes** in router files 