"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Header } from "@/components/ui/header";

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-sky-100 via-indigo-50 to-purple-100">
      <Header />
      <div className="flex flex-1 flex-col items-center justify-center text-center gap-6 p-6">
        <h1 className="text-6xl font-bold text-navy-700">404</h1>
        <p className="text-xl text-navy-600">הדף המבוקש לא נמצא</p>
        <Button asChild>
          <Link href="/">חזרה לדף הבית</Link>
        </Button>
      </div>
    </div>
  );
}
