import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { ReportType } from "@prisma/client";

// Define a proper type for report parameters
interface ReportParameters {
  format?: string;
  startDate?: string;
  endDate?: string;
  employerId?: string | null;
}

// Centralized report name mappings
const REPORT_NAMES: Record<string, string> = {
  "FORM_102": "דוח שימוש במערכת",
  "PAYROLL_SUMMARY": "דוח שגיאות",
  "TAX_REPORT": "פעילות מעסיקים",
  "EMPLOYEE_OVERVIEW": "סקירת עובדים"
};

export const reportRouter = createTRPCRouter({
  getAvailableReports: protectedProcedure.query(async ({ ctx }) => {
    try {
      const { db, session } = ctx;
      const userId = session.user.id;
      
      // Get the tenant ID for the current user
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { tenantId: true }
      });
      
      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }
      
      // Define the available reports
      const reportTypes = [
        {
          id: "FORM_102",
          name: "דוח שימוש במערכת",
          description: "ניתוח השימוש במערכת לפי מעסיק",
          details: "צפייה באנליטיקות מפורטות על אופן השימוש של מעסיקים במערכת, כולל תדירות, זמני שימוש שיא, וניצול תכונות."
        },
        {
          id: "PAYROLL_SUMMARY",
          name: "דוח שגיאות",
          description: "שגיאות מערכת וחריגות",
          details: "ניתוח שגיאות מערכת, חריגות ובעיות ביצועים בכל המעסיקים לזיהוי ופתרון בעיות."
        },
        {
          id: "TAX_REPORT",
          name: "פעילות מעסיקים",
          description: "סיכום פעילות מעסיקים",
          details: "סקירה של פעילויות מעסיקים אחרונות, כולל עיבוד שכר, הגשת טפסים וכניסות משתמשים."
        }
      ];
      
      return {
        reports: reportTypes.map(report => ({
          id: report.id,
          title: report.name,
          description: report.description,
          details: report.details
        }))
      };
    } catch (error) {
      ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error fetching report types");
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch report types",
        cause: error,
      });
    }
  }),

  generateReport: protectedProcedure
    .input(z.object({ 
      reportId: z.nativeEnum(ReportType),
      parameters: z.object({
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        employerId: z.string().optional(),
        format: z.enum(["pdf", "excel", "csv"]).optional().default("pdf")
      }).optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Use the centralized report names constant
        const reportName = REPORT_NAMES[input.reportId] || "דוח מערכת";
        
        // Get employerId if provided
        let employerId: string | null = null;
        if (input.parameters?.employerId) {
          employerId = input.parameters.employerId;
        }
        
        // Create a new report record
        const now = new Date();
        const report = await db.report.create({
          data: {
            tenantId: user.tenantId,
            type: input.reportId,
            year: now.getFullYear(),
            month: now.getMonth() + 1,
            parameters: {
              startDate: input.parameters?.startDate,
              endDate: input.parameters?.endDate,
              employerId: employerId,
              format: input.parameters?.format
            },
            filePath: `/reports/${input.reportId}_${now.getTime()}.${input.parameters?.format || "pdf"}`,
            generatedBy: userId,
            employerId: employerId
          }
        });
        
        // Create audit log entry
        const auditLog = await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            modelName: "Report",
            recordId: report.id,
            action: "CREATE",
            userId: userId
          }
        });

        // Create audit log values
        const auditValues = [];
        auditValues.push({
          auditLogId: auditLog.id,
          valueType: 'new',
          fieldName: 'reportType',
          fieldValue: input.reportId,
          dataType: 'string'
        });
        if (input.parameters) {
          auditValues.push({
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'parameters',
            fieldValue: JSON.stringify(input.parameters),
            dataType: 'object'
          });
        }

        await db.auditLogValues.createMany({
          data: auditValues
        });
        
        return {
          success: true,
          reportUrl: `/api/reports/download/${report.id}`,
          reportName: reportName,
          generatedAt: report.generatedAt.toISOString()
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error generating report");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate report",
          cause: error,
        });
      }
    }),

  getRecentReports: protectedProcedure
    .input(z.object({
      limit: z.number().default(5)
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Get recent reports
        const reports = await db.report.findMany({
          where: {
            tenantId: user.tenantId
          },
          include: {
            generator: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            generatedAt: "desc"
          },
          take: input.limit
        });
        
        return {
          reports: reports.map(report => ({
            id: report.id,
            title: REPORT_NAMES[report.type] || "דוח מערכת",
            type: report.type,
            generatedAt: report.generatedAt.toISOString(),
            generatedBy: report.generator?.name || "מערכת",
            downloadUrl: `/api/reports/download/${report.id}`,
            format: ((report.parameters as ReportParameters)?.format) || "pdf"
          }))
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error fetching recent reports");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch recent reports",
          cause: error,
        });
      }
    }),
});

// Helper function to get report name based on ID
function getReportName(reportId: string): string {
  return REPORT_NAMES[reportId] || "דוח מערכת";
} 