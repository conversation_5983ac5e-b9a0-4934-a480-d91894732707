"use client";

import {
	Table,
	TableHeader,
	TableRow,
	TableHead,
	TableBody,
	TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";
import type { Formula } from "../hooks";

interface FormulaTableProps {
	formulas: Formula[];
	isLoading: boolean;
	onEdit: (formula: Formula) => void;
	onDelete: (formula: Formula) => void;
}

export function FormulaTable({
	formulas,
	isLoading,
	onEdit,
	onDelete,
}: FormulaTableProps) {
	if (isLoading) {
		return (
			<div className="space-y-2">
				{Array.from({ length: 5 }).map((_, i) => (
					<Skeleton key={i} className="h-12 w-full" />
				))}
			</div>
		);
	}

	return (
		<div className="rounded-md border overflow-x-auto">
			<Table className="text-right rtl:text-right">
				<TableHeader>
					<TableRow>
						<TableHead>שם נוסחה</TableHead>
						<TableHead>תיאור</TableHead>
						<TableHead>סוג</TableHead>
						<TableHead>תאריך התחלה</TableHead>
						<TableHead>תאריך סיום</TableHead>
						<TableHead>סטטוס</TableHead>
						<TableHead className="w-32">פעולות</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{formulas.map((formula) => (
						<TableRow key={formula.id}>
							<TableCell>{formula.name}</TableCell>
							<TableCell>{formula.description}</TableCell>
							<TableCell>{formula.type}</TableCell>
							<TableCell>
								{format(new Date(formula.startDate), "MM/yyyy")}
							</TableCell>
							<TableCell>
								{formula.endDate
									? format(new Date(formula.endDate), "MM/yyyy")
									: ""}
							</TableCell>
							<TableCell>{formula.status}</TableCell>
							<TableCell>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => onEdit(formula)}
									>
										עריכה
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => onDelete(formula)}
									>
										מחיקה
									</Button>
								</div>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
			{formulas.length === 0 && (
				<div className="p-6 text-center text-gray-500">לא נמצאו נוסחאות</div>
			)}
		</div>
	);
}
