"use client";

import { api } from "@/trpc/react";
import { toast } from "sonner";

export type FormulaType = "OTHER" | "PENSION" | "TAX" | "DEDUCTION" | "BENEFIT";
export type FormulaStatus = "ACTIVE" | "DRAFT" | "INACTIVE";

export type Formula = {
	id: string;
	name: string;
	description?: string | null;
	type: FormulaType;
	startDate: string;
	endDate?: string | null;
	status: FormulaStatus;
	formulaCode: string;
};

export interface FormulaFilters {
	search?: string;
	type?: FormulaType;
	status?: FormulaStatus;
}

export function useFormulas(filters?: FormulaFilters) {
	return api.formula.getAll.useQuery(filters);
}

export function useCreateFormula() {
	const utils = api.useUtils();
	return api.formula.create.useMutation({
		onSuccess: () => {
			utils.formula.getAll.invalidate();
			toast.success("נוסחה נוצרה בהצלחה");
		},
	});
}

export function useUpdateFormula() {
	const utils = api.useUtils();
	return api.formula.update.useMutation({
		onSuccess: () => {
			utils.formula.getAll.invalidate();
			toast.success("נוסחה עודכנה בהצלחה");
		},
	});
}

export function useDeleteFormula() {
	const utils = api.useUtils();
	return api.formula.delete.useMutation({
		onSuccess: () => {
			utils.formula.getAll.invalidate();
			toast.success("נוסחה נמחקה");
		},
	});
}
