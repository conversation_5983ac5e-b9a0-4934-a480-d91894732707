"use client";

import { api } from "@/trpc/react";
import { toast } from "sonner";
import type {
  PaymentComponent,
  PaymentComponentFilters,
  PaymentComponentFormData,
} from "./types";

// ============================================
// Payment Components Hooks
// ============================================

export function usePaymentComponents(filters?: PaymentComponentFilters) {
  return api.paymentComponent.getAll.useQuery(filters);
}

export function usePaymentComponent(id: string) {
  return api.paymentComponent.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreatePaymentComponent() {
    const utils = api.useUtils();

  return api.paymentComponent.create.useMutation({
    onSuccess: () => {
      utils.paymentComponent.getAll.invalidate();
      toast.success("רכיב תשלום נוצר בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה ביצירת רכיב תשלום: ${error.message}`);
    },
  });
}

export function useUpdatePaymentComponent() {
    const utils = api.useUtils();

  return api.paymentComponent.update.useMutation({
    onSuccess: (data) => {
      utils.paymentComponent.getAll.invalidate();
      utils.paymentComponent.getById.invalidate({ id: data.id });
      toast.success("רכיב תשלום עודכן בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בעדכון רכיב תשלום: ${error.message}`);
    },
  });
}

export function useDeletePaymentComponent() {
    const utils = api.useUtils();

  return api.paymentComponent.delete.useMutation({
    onSuccess: () => {
      utils.paymentComponent.getAll.invalidate();
      toast.success("רכיב תשלום נמחק בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה במחיקת רכיב תשלום: ${error.message}`);
    },
  });
}

// ============================================
// Reports and Analytics Hooks
// ============================================

export function usePaymentChangeReport(fromDate: Date, toDate: Date) {
  return api.paymentComponent.getChangeReport.useQuery(
    { fromDate, toDate },
    { enabled: !!fromDate && !!toDate }
  );
}

export function usePaymentUsageStats() {
  return api.paymentComponent.getUsageStats.useQuery();
}

// ============================================
// Validation Hooks
// ============================================

export function useValidatePaymentComponent(data: PaymentComponentFormData) {
  return api.paymentComponent.validate.useQuery(
    data,
    { enabled: !!data.name && !!data.code }
  );
}

// ============================================
// Bulk Operations Hooks
// ============================================

export function useBulkUpdatePaymentComponents() {
    const utils = api.useUtils();

  return api.paymentComponent.bulkUpdate.useMutation({
    onSuccess: (data) => {
      utils.paymentComponent.getAll.invalidate();
      toast.success(`${data.updated} רכיבי תשלום עודכנו בהצלחה`);
    },
    onError: (error) => {
      toast.error(`שגיאה בעדכון רכיבי תשלום: ${error.message}`);
    },
  });
}

export function useDuplicatePaymentComponent() {
    const utils = api.useUtils();

  return api.paymentComponent.duplicate.useMutation({
    onSuccess: () => {
      utils.paymentComponent.getAll.invalidate();
      toast.success("רכיב תשלום שוכפל בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בשכפול רכיב תשלום: ${error.message}`);
    },
  });
}

// ============================================
// Export/Import Hooks
// ============================================

export function useExportPaymentComponents() {
  return api.paymentComponent.export.useMutation({
    onSuccess: (data) => {
      // Trigger download
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `payment-components-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success("קובץ רכיבי התשלום הורד בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בייצוא: ${error.message}`);
    },
  });
}

export function useImportPaymentComponents() {
    const utils = api.useUtils();

  return api.paymentComponent.import.useMutation({
    onSuccess: (data) => {
      utils.paymentComponent.getAll.invalidate();
      toast.success(`${data.imported} רכיבי תשלום יובאו בהצלחה`);
    },
    onError: (error) => {
      toast.error(`שגיאה בייבוא: ${error.message}`);
    },
  });
}
