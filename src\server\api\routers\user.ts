import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";

import { mapHebrewToRole, mapRoleToHebrew } from "@/server/utils/roleMapping";

import { Role, Prisma } from "@prisma/client";

import bcrypt from "bcryptjs";

export const userRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().default(1),
        limit: z.number().default(10),
        status: z.enum(["active", "inactive", "all"]).optional().default("all"),
        employerId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        const isPrivileged =
          session.user.role === "OWNER" || session.user.role === "ADMIN";
        if (
          !isPrivileged &&
          input.employerId &&
          session.user.employerId &&
          input.employerId !== session.user.employerId
        ) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Employer mismatch",
          });
        }

        const employerIdFilter = isPrivileged
          ? input.employerId
          : session.user.employerId;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true },
        });

        if (!user) {
          ctx.logger?.error(
            { userId },
            "User ID from session not found in database"
          );
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `User with ID ${userId} not found in database`,
          });
        }

        // Calculate pagination
        const skip = (input.page - 1) * input.limit;

        // Build where clause based on filters
        const where: Prisma.UserWhereInput = {
          tenantId: user.tenantId,
        };

        let filteredUserIds: string[] | undefined = undefined;

        if (employerIdFilter) {          // Find user IDs whose audit log has the desired employerId directly using JSON path
          const auditLogs = await db.auditLog.findMany({
            where: {
              tenantId: user.tenantId,
              modelName: "User",
              action: { in: ["CREATE", "UPDATE"] },
              values: {
                some: {
                  fieldName: "employerId",
                  fieldValue: employerIdFilter,
                }
              },
            },
            distinct: ["recordId"],
            select: {
              recordId: true,
            },
          });

          filteredUserIds = auditLogs
            .map((log) => log.recordId)
            .filter((id): id is string => id !== null);

          // If no users match, return empty result early
          if (filteredUserIds.length === 0) {
            return {
              users: [],
              totalCount: 0,
              pageCount: 0,
            };
          }

          where.id = { in: filteredUserIds };
        }

        // Add status filter if not "all"
        if (input.status !== "all") {
          where.isActive = input.status === "active";
        }

        // Get total count for pagination
        const totalCount = await db.user.count({ where });        // Get users with additional data
        const users = await db.user.findMany({
          where,
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            isActive: true,
          },
          orderBy: {
            name: "asc",
          },
          skip,
          take: input.limit,
        });

        // קבל את כל המעסיקים עבור השוכר הנוכחי
        const employers = await db.employer.findMany({
          where: { tenantId: user.tenantId },
          select: {
            id: true,
            name: true,
          },
        });        // נשיג מידע על מעסיקים מהאודיט לוג
        const userAuditLogs = await db.auditLog.findMany({
          where: {
            tenantId: user.tenantId,
            modelName: "User",
            recordId: { in: users.map((u) => u.id) },
            action: { in: ["CREATE", "UPDATE"] },
          },
          include: {
            values: {
              where: {
                fieldName: "employerId",
                valueType: "new",
              },
            },
          },
          orderBy: {
            timestamp: "desc",
          },
        });

        // מיפוי של היסטוריית employerId לפי משתמש
        const userEmployers = new Map<string, string>();

        for (const auditLog of userAuditLogs) {
          if (!auditLog.recordId) continue;

          // אם יש לנו כבר מעסיק למשתמש זה, נדלג (כי כבר יש לנו את העדכני ביותר)
          if (userEmployers.has(auditLog.recordId)) continue;

          const employerIdValue = auditLog.values.find(
            (value) => value.fieldName === "employerId" && value.valueType === "new"
          );
          
          if (employerIdValue?.fieldValue) {
            userEmployers.set(auditLog.recordId, employerIdValue.fieldValue);
          }
        }

        return {
          users: users.map((user) => {
            // נמצא את ה-employerId בהיסטוריית האודיט
            let employerId = userEmployers.get(user.id) || "";
            let employerName = "";

            // אם יש לנו employerId, נמצא את שם המעסיק
            if (employerId) {
              const employer = employers.find((e) => e.id === employerId);
              if (employer) {
                employerName = employer.name;
              }
            }

            // Don't default to first employer if none is found

            return {
              id: user.id,
              name: user.name || "",
              email: user.email,
              role: mapRoleToHebrew(user.role),
              employerId,
              employerName,
              status: user.isActive ? "active" : "inactive",
            };
          }),
          totalCount,
          pageCount: Math.ceil(totalCount / input.limit),
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id },
          "Error fetching users"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch users",
          cause: error,
        });
      }
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const currentUserId = session.user.id;
        const targetUserId = input.id;

        // Get the tenant ID for the current user
        const currentUser = await db.user.findUnique({
          where: { id: currentUserId },
          select: { tenantId: true },
        });

        if (!currentUser) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Get target user
        const user = await db.user.findFirst({
          where: {
            id: targetUserId,
            tenantId: currentUser.tenantId,
          },
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // נחפש את היחס למעסיק באמצעות האודיט לוג
        let employerId = "";
        let employerName = "";        // חיפוש ברשומות האודיט לגבי המשתמש הזה
        const latestAuditLog = await db.auditLog.findFirst({
          where: {
            tenantId: currentUser.tenantId,
            modelName: "User",
            recordId: targetUserId,
            action: { in: ["CREATE", "UPDATE"] },
          },
          include: {
            values: {
              where: {
                fieldName: "employerId",
                valueType: "new",
              },
            },
          },
          orderBy: {
            timestamp: "desc",
          },
        });        const employerIdValue = latestAuditLog?.values.find(
          (value) => value.fieldName === "employerId" && value.valueType === "new"
        );
        
        if (employerIdValue?.fieldValue) {
          employerId = employerIdValue.fieldValue;

          // נחפש את שם המעסיק לפי ה-ID
          if (employerId) {
            const employer = await db.employer.findFirst({
              where: {
                id: employerId,
                tenantId: currentUser.tenantId,
              },
              select: { name: true },
            });

            if (employer) {
              employerName = employer.name;
            }
          }
        }

        return {
          id: user.id,
          name: user.name || "",
          email: user.email,
          role: mapRoleToHebrew(user.role),
          employerId,
          employerName,
          status: user.isActive ? "active" : "inactive",
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id },
          "Error fetching user"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch user",
          cause: error,
        });
      }
    }),

  create: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1),
        email: z.string().email(),
        role: z.string().min(1),
        employerId: z.string().uuid(),
        status: z.enum(["active", "inactive"]).default("active"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get the tenant ID for the current user
        const currentUser = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true, role: true },
        });

        if (!currentUser) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if the current user has permission to create users
        if (currentUser.role !== "OWNER" && currentUser.role !== "ADMIN") {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You don't have permission to create users",
          });
        }        // Find employer associated with the current user via audit log
        const latestAudit = await db.auditLog.findFirst({
          where: {
            tenantId: currentUser.tenantId,
            modelName: "User",
            recordId: userId,
            action: { in: ["CREATE", "UPDATE"] },
          },
          include: {
            values: {
              where: {
                fieldName: "employerId",
                valueType: "new",
              },
            },
          },
          orderBy: { timestamp: "desc" },
        });

        let currentEmployerId: string | null = null;
        const employerIdValue = latestAudit?.values.find(
          (value) => value.fieldName === "employerId" && value.valueType === "new"
        );
        
        if (employerIdValue?.fieldValue) {
          currentEmployerId = employerIdValue.fieldValue;
        }

        if (currentEmployerId && input.employerId !== currentEmployerId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Employer mismatch",
          });
        }

        // Check if email is already in use in this tenant
        const existingUser = await db.user.findFirst({
          where: {
            tenantId: currentUser.tenantId,
            email: input.email,
          },
        });

        if (existingUser) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Email is already in use",
          });
        }

        // Map role string to enum
        const role = mapHebrewToRole(input.role);

        // Create a temporary password (in a real app, you'd generate a random one)
        const tempPassword = "ChangeMe123!";
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(tempPassword, saltRounds);

        // Create the new user
        const user = await db.user.create({
          data: {
            name: input.name,
            email: input.email,
            role: role,
            password: hashedPassword,
            isActive: input.status === "active",
            tenant: {
              connect: {
                id: currentUser.tenantId,
              },
            },
          },
        });

        // וודא שה-employerId קיים
        if (!input.employerId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Employer ID is required",
          });
        }

        // נמצא את שם המעסיק
        let employerName = "";

        // מנסה למצוא את המעסיק לפי ה-ID
        const employer = await db.employer.findFirst({
          where: {
            id: input.employerId,
            tenantId: currentUser.tenantId,
          },
          select: { name: true },
        });

        if (!employer) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Employer not found in current tenant",
          });
        }

        employerName = employer.name;        // Create audit log entry with employerId
        const auditLog = await db.auditLog.create({
          data: {
            tenantId: currentUser.tenantId,
            userId: userId,
            action: "CREATE",
            modelName: "User",
            recordId: user.id,
          },
        });

        // Create audit log values
        const auditValues = [
          {
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'name',
            fieldValue: input.name,
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'email',
            fieldValue: input.email,
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'role',
            fieldValue: role,
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'employerId',
            fieldValue: input.employerId,
            dataType: 'string'
          }
        ];

        await db.auditLogValues.createMany({
          data: auditValues,
        });

        return {
          id: user.id,
          name: user.name || "",
          email: user.email,
          role: mapRoleToHebrew(user.role),
          employerId: input.employerId,
          employerName,
          status: user.isActive ? "active" : "inactive",
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id },
          "Error creating user"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create user",
          cause: error,
        });
      }
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        email: z.string().email().optional(),
        role: z.string().min(1).optional(),
        employerId: z.string().uuid().optional(),
        status: z.enum(["active", "inactive"]).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const currentUserId = session.user.id;
        const targetUserId = input.id;

        // Get the tenant ID for the current user
        const currentUser = await db.user.findUnique({
          where: { id: currentUserId },
          select: { tenantId: true, role: true },
        });

        if (!currentUser) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if the current user has permission to update users
        if (currentUser.role !== "OWNER" && currentUser.role !== "ADMIN") {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You don't have permission to update users",
          });
        }

        // Get existing user to update
        const existingUser = await db.user.findFirst({
          where: {
            id: targetUserId,
            tenantId: currentUser.tenantId,
          },
        });

        if (!existingUser) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Prepare data for update
        const updateData: any = {};
        if (input.name !== undefined) updateData.name = input.name;
        if (input.email !== undefined) {
          // Check if email is already in use by another user
          if (input.email !== existingUser.email) {
            const emailExists = await db.user.findFirst({
              where: {
                tenantId: currentUser.tenantId,
                email: input.email,
                id: { not: targetUserId },
              },
            });

            if (emailExists) {
              throw new TRPCError({
                code: "CONFLICT",
                message: "Email is already in use",
              });
            }
          }
          updateData.email = input.email;
        }
        if (input.role !== undefined) {
          updateData.role = mapHebrewToRole(input.role);
        }
        if (input.status !== undefined) {
          updateData.isActive = input.status === "active";
        }

        // Update the user
        const updatedUser = await db.user.update({
          where: {
            id: targetUserId,
          },
          data: updateData,
        });

        // נמצא את שם המעסיק אם נשלח ID
        let employerId = input.employerId || "";
        let employerName = "";

        if (employerId) {
          // מנסה למצוא את המעסיק לפי ה-ID
          const employer = await db.employer.findUnique({
            where: {
              id: employerId,
              tenantId: currentUser.tenantId,
            },
            select: { name: true },
          });

          if (!employer) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Employer not found in current tenant",
            });
          }

          employerName = employer.name;
        }        // Create audit log entry including employerId
        const auditLog = await db.auditLog.create({
          data: {
            tenantId: currentUser.tenantId,
            userId: currentUserId,
            action: "UPDATE",
            modelName: "User",
            recordId: targetUserId,
          },
        });

        // Create audit log values for old values
        const oldValues = [
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'name',
            fieldValue: existingUser.name || '',
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'email',
            fieldValue: existingUser.email,
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'role',
            fieldValue: existingUser.role,
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'isActive',
            fieldValue: String(existingUser.isActive),
            dataType: 'boolean'
          }
        ];

        // Create audit log values for new values
        const newValues = [];
        if (updateData.name !== undefined) {
          newValues.push({
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'name',
            fieldValue: updateData.name || '',
            dataType: 'string'
          });
        }
        if (updateData.email !== undefined) {
          newValues.push({
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'email',
            fieldValue: updateData.email,
            dataType: 'string'
          });
        }
        if (updateData.role !== undefined) {
          newValues.push({
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'role',
            fieldValue: updateData.role,
            dataType: 'string'
          });
        }
        if (updateData.isActive !== undefined) {
          newValues.push({
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'isActive',
            fieldValue: String(updateData.isActive),
            dataType: 'boolean'
          });
        }
        if (input.employerId) {
          newValues.push({
            auditLogId: auditLog.id,
            valueType: 'new',
            fieldName: 'employerId',
            fieldValue: input.employerId,
            dataType: 'string'
          });
        }

        await db.auditLogValues.createMany({
          data: [...oldValues, ...newValues],
        });

        return {
          success: true,
          user: {
            id: updatedUser.id,
            name: updatedUser.name || "",
            email: updatedUser.email,
            role: mapRoleToHebrew(updatedUser.role),
            employerId,
            employerName,
            status: updatedUser.isActive ? "active" : "inactive",
          },
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id },
          "Error updating user"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user",
          cause: error,
        });
      }
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const currentUserId = session.user.id;
        const targetUserId = input.id;

        // Prevent self-deletion
        if (currentUserId === targetUserId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot delete your own account",
          });
        }

        // Get the tenant ID for the current user
        const currentUser = await db.user.findUnique({
          where: { id: currentUserId },
          select: { tenantId: true, role: true },
        });

        if (!currentUser) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if the current user has permission to delete users
        if (currentUser.role !== "OWNER" && currentUser.role !== "ADMIN") {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You don't have permission to delete users",
          });
        }

        // Get existing user to delete
        const existingUser = await db.user.findFirst({
          where: {
            id: targetUserId,
            tenantId: currentUser.tenantId,
          },
        });

        if (!existingUser) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Prevent deleting the last OWNER
        if (existingUser.role === "OWNER") {
          const ownerCount = await db.user.count({
            where: {
              tenantId: currentUser.tenantId,
              role: "OWNER",
            },
          });

          if (ownerCount <= 1) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Cannot delete the last owner account",
            });
          }
        }

        // Delete the user
        await db.user.delete({
          where: {
            id: targetUserId,
          },
        });        // Create audit log entry
        const auditLog = await db.auditLog.create({
          data: {
            tenantId: currentUser.tenantId,
            userId: currentUserId,
            action: "DELETE",
            modelName: "User",
            recordId: targetUserId,
          },
        });

        // Create audit log values for old values
        const oldValues = [
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'id',
            fieldValue: existingUser.id,
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'name',
            fieldValue: existingUser.name || '',
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'email',
            fieldValue: existingUser.email,
            dataType: 'string'
          },
          {
            auditLogId: auditLog.id,
            valueType: 'old',
            fieldName: 'role',
            fieldValue: existingUser.role,
            dataType: 'string'
          }
        ];

        await db.auditLogValues.createMany({
          data: oldValues,
        });

        return { success: true };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error deleting user");
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete user",
          cause: error,
        });
      }
    }),
});

