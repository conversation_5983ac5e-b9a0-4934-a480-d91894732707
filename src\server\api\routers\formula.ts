import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { z } from "zod";
import { TRPCError } from "@trpc/server";

const formulaTypeEnum = z.enum([
	"TAX",
	"DEDUCTION",
	"PEN<PERSON>ON",
	"BENEFIT",
	"OTHER",
]);
const formulaStatusEnum = z.enum(["ACTIVE", "INACTIVE", "DRAFT"]);

const formulaBaseSchema = z.object({
	name: z.string().min(1),
	description: z.string().optional(),
	type: formulaTypeEnum,
	startDate: z.string(),
	endDate: z.string().optional(),
	status: formulaStatusEnum.default("DRAFT"),
	formulaCode: z.string().min(1),
});

export const formulaRouter = createTRPCRouter({
	getAll: publicProcedure
		.input(
			z
				.object({
					search: z.string().optional(),
					type: formulaTypeEnum.optional(),
					status: formulaStatusEnum.optional(),
				})
				.optional(),
		)
		.query(async ({ ctx, input }) => {
			const where: Record<string, unknown> = {};
			if (input?.search) {
				where.OR = [
					{ name: { contains: input.search, mode: "insensitive" } },
					{ description: { contains: input.search, mode: "insensitive" } },
				];
			}
			if (input?.type) where.type = input.type;
			if (input?.status) where.status = input.status;

			return ctx.db.formula.findMany({
				where,
				orderBy: { startDate: "desc" },
			});
		}),

	getById: publicProcedure
		.input(z.object({ id: z.string() }))
		.query(({ ctx, input }) =>
			ctx.db.formula.findUnique({ where: { id: input.id } }),
		),
	create: protectedProcedure
		.input(formulaBaseSchema)		.mutation(async ({ ctx, input }) => {
			const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
			const employerId = ctx.session.user.employerId ?? "default"; // Get employerId from session
			return ctx.db.formula.create({ data: { ...input, tenantId, employerId } });
		}),

	update: publicProcedure
		.input(formulaBaseSchema.extend({ id: z.string() }))
		.mutation(({ ctx, input }) => {
			const { id, ...data } = input;
			return ctx.db.formula.update({ where: { id }, data });
		}),

	delete: publicProcedure
		.input(z.object({ id: z.string() }))
		.mutation(({ ctx, input }) =>
			ctx.db.formula.delete({ where: { id: input.id } }),
		),
});
