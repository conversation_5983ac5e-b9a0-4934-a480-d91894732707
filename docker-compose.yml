# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=**************************************/salary?schema=public
      - NEXTAUTH_SECRET=supersecret
      - NEXTAUTH_URL=http://localhost:3000
    command: sh -c "pnpm db:push && pnpm dev"

  db:
    image: postgres:16-alpine
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=salary
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
