"use client";

import { useEffect, useState, type ReactNode } from "react";
import { createPortal } from "react-dom";
import { useMasterDetail } from "../layout";

interface DetailViewProps {
	children: ReactNode;
	itemId: string;
	title?: string;
}

export function DetailView({ children, itemId, title }: DetailViewProps) {
	const { setSelectedItemId, setIsDetailOpen } = useMasterDetail();

	useEffect(() => {
		// When this component mounts, update the context
		setSelectedItemId(itemId);
		setIsDetailOpen(true);

		// Cleanup when unmounting
		return () => {
			setSelectedItemId(null);
			setIsDetailOpen(false);
		};
	}, [itemId, setSelectedItemId, setIsDetailOpen]);

	// Wait for the detail container to be available
	const [mounted, setMounted] = useState(false);
	useEffect(() => {
		setMounted(true);
	}, []);

	if (!mounted) return null;

	const detailContainer = document.getElementById("detail-content");
	if (!detailContainer) return null;

	return createPortal(
		<div className="space-y-4">
			{title && (
				<h3 className="text-lg font-semibold border-b pb-2">{title}</h3>
			)}
			{children}
		</div>,
		detailContainer
	);
}

// Hook to control detail view from any component
export function useDetailView() {
	const { selectedItemId, setSelectedItemId, isDetailOpen, setIsDetailOpen } = useMasterDetail();

	const openDetail = (itemId: string) => {
		setSelectedItemId(itemId);
		setIsDetailOpen(true);
	};

	const closeDetail = () => {
		setSelectedItemId(null);
		setIsDetailOpen(false);
	};

	return {
		selectedItemId,
		isDetailOpen,
		openDetail,
		closeDetail,
	};
} 