/**
 * This is the client-side entrypoint for your tRPC API.
 * Re-export of the tRPC React hooks for compatibility.
 */
import { type inferRouterInputs, type inferRouterOutputs } from "@trpc/server";

import { type AppRouter } from "@/server/api/root";
import { api as trpcApi } from "@/trpc/react";

// Re-export the api for compatibility with existing code
export const api = trpcApi;

/**
 * Inference helper for inputs.
 *
 * @example type HelloInput = RouterInputs['example']['hello']
 */
export type RouterInputs = inferRouterInputs<AppRouter>;

/**
 * Inference helper for outputs.
 *
 * @example type HelloOutput = RouterOutputs['example']['hello']
 */
export type RouterOutputs = inferRouterOutputs<AppRouter>;