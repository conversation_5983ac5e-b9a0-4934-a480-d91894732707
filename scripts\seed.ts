import {
  PrismaClient,
  Role,
  Sector,
  AgreementType,
  PayslipStatus,
  AlertType,
  AlertCategory,
  MaritalStatus,
  AgreementStatus,
  DayType,
  MovementCategory,
  MovementSource,
  DeductionType,
  TaxCalculationType,
  SocialSecurityCalculationType,
  Form101Status,
  PayslipItemKod,
  PayslipItemType,
  AssociationType,
  Basis,
  Currency,
  PayFrequency,
  PaymentType,
  FormulaType,
  FormulaStatus,
  ValueComponentType
} from "@prisma/client";
import bcrypt from "bcryptjs";

/**
 * Enhanced seed script for foreign worker payroll system
 * Generates comprehensive test data with multiple scenarios
 */

// 2025 Israeli minimum wage constants for foreign workers
const MIN_MONTHLY_SALARY = 6247.67;  // Foreign worker minimum monthly salary 2025
const MIN_HOURLY_WAGE = 36.45;       // Foreign worker minimum hourly wage 2025
const MONTHLY_HOURS = 171.4;         // Standard monthly hours for foreign workers

// 2025 Deduction rates for foreign workers
const DEDUCTION_RATES = {
  SOCIAL_SECURITY_EMPLOYEE: 0.0085,    // 0.85% (vs 7% for Israelis)
  SOCIAL_SECURITY_EMPLOYER: 0.0352,    // 3.52% (vs 7.6% for Israelis)
  HEALTH_INSURANCE: 0.031,              // 3.1% but capped at ₪143.72/month
  HEALTH_INSURANCE_MAX: 143.72,         // Maximum monthly deduction
  PENSION_EMPLOYEE: 0.06,               // 6% (same as Israelis)
  PENSION_EMPLOYER: 0.065,              // 6.5% (same as Israelis)
  SEVERANCE_PAY: 0.083                  // 8.33% (same as Israelis)
};

// 2025 Overtime rates
const OVERTIME_RATES = {
  RATE_125: 1.25,  // 125% for first overtime hours
  RATE_150: 1.50,  // 150% for weekend/holiday work
  RATE_175: 1.75,  // 175% for excessive overtime
  RATE_200: 2.00,  // 200% for holidays
};

// 2025 Tax brackets (מדרגות מס)
const TAX_BRACKETS_2025 = [
  { from: 0, to: 7010, rate: 0.10 },
  { from: 7010, to: 10060, rate: 0.14 },
  { from: 10060, to: 16150, rate: 0.20 },
  { from: 16150, to: 22440, rate: 0.31 },
  { from: 22440, to: 46690, rate: 0.35 },
  { from: 46690, to: 60130, rate: 0.47 },
  { from: 60130, to: Infinity, rate: 0.50 }
];

// 2025 Social Security rates by employee type
const SOCIAL_SECURITY_RATES_2025 = {
  // Regular Israeli employee (18 to retirement age)
  ISRAELI_REGULAR: {
    reduced: { employee: 0.0427, health: 0.0323, employer: 0.0451 },
    full: { employee: 0.1217, health: 0.0517, employer: 0.076 }
  },
  // Foreign worker
  FOREIGN_WORKER: {
    reduced: { employee: 0.001, health: 0, employer: 0.0075 },
    full: { employee: 0.0087, health: 0, employer: 0.0265 }
  },
  // Foreign worker from treaty country
  FOREIGN_WORKER_TREATY: {
    reduced: { employee: 0.0104, health: 0, employer: 0.0451 },
    full: { employee: 0.07, health: 0, employer: 0.076 }
  },
  // Palestinian worker
  PALESTINIAN_WORKER: {
    reduced: { employee: 0.0007, health: 0, employer: 0.0071 },
    full: { employee: 0.0061, health: 0, employer: 0.0249 }
  }
};

// 2025 Social Security thresholds
const SOCIAL_SECURITY_THRESHOLDS_2025 = {
  REDUCED_RATE_CEILING: 7522,     // תקרה לשיעור מופחת
  FULL_RATE_CEILING: 50695,       // תקרה לתשלום דמי ביטוח
  HEALTH_INSURANCE_MAX: 143.72    // תקרת ביטוח בריאות לעובדים זרים
};

// 2025 Tax credits and allowances
const TAX_CREDITS_2025 = {
  CREDIT_POINT_VALUE: 242,        // ערך נקודת זיכוי חודשית
  CREDIT_POINT_VALUE_ANNUAL: 2904, // ערך נקודת זיכוי שנתית
  WORK_INCOME_CEILING_MONTHLY: 9700,  // תקרת הכנסה מזכה - הכנסת עבודה
  WORK_INCOME_CEILING_ANNUAL: 116400,
  NON_WORK_INCOME_CEILING_MONTHLY: 13700, // תקרת הכנסה מזכה - לא הכנסת עבודה
  NON_WORK_INCOME_CEILING_ANNUAL: 164400,
  PREFERRED_NON_WORK_INCOME_CEILING_MONTHLY: 19400, // עמית מוטב
  PREFERRED_NON_WORK_INCOME_CEILING_ANNUAL: 232800,
  MAX_SAVINGS_EMPLOYEE_MONTHLY: 679,  // חסכון מרבי שכיר
  MAX_SAVINGS_EMPLOYEE_ANNUAL: 8148,
  MAX_SAVINGS_SELF_EMPLOYED_MONTHLY: 753.5, // חסכון מרבי עצמאי
  MAX_SAVINGS_SELF_EMPLOYED_ANNUAL: 9042,
  MIN_SAVINGS_MONTHLY: 189,
  MIN_SAVINGS_ANNUAL: 2268
};

// Simple random generators
const randomInt = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min;
const randomDate = (start: Date, end: Date) => new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
const randomPastDate = (years: number) => randomDate(new Date(Date.now() - years * 365 * 24 * 60 * 60 * 1000), new Date());
const randomFutureDate = (years: number) => randomDate(new Date(), new Date(Date.now() + years * 365 * 24 * 60 * 60 * 1000));
const randomNumericString = (length: number) => Array.from({length}, () => randomInt(0, 9)).join('');

// Generate names based on country
const generateWorkerName = (country: string): { firstName: string; lastName: string } => {
  const namesByCountry: Record<string, { firstNames: string[], lastNames: string[] }> = {
    Thailand: {
      firstNames: ["Somchai", "Somsak", "Anong", "Apinya", "Chai", "Chatri", "Decha", "Kiet", "Mongkut", "Niran"],
      lastNames: ["Jaidee", "Srisuk", "Chaiyasarn", "Thongchai", "Saetang", "Suwannarat", "Ritthisak", "Wongsawat"]
    },
    Philippines: {
      firstNames: ["Juan", "Maria", "Jose", "Ana", "Pedro", "Rosa", "Carlos", "Elena", "Miguel", "Sofia"],
      lastNames: ["Santos", "Cruz", "Garcia", "Reyes", "Ramos", "Mendoza", "Lopez", "Gonzales"]
    },
    China: {
      firstNames: ["Wei", "Li", "Zhang", "Wang", "Chen", "Liu", "Huang", "Zhou", "Wu", "Xu"],
      lastNames: ["Li", "Wang", "Zhang", "Liu", "Chen", "Yang", "Huang", "Zhao", "Wu", "Zhou"]
    },
    Moldova: {
      firstNames: ["Ion", "Maria", "Nicolae", "Elena", "Vasile", "Ana", "Gheorghe", "Natalia", "Andrei", "Tatiana"],
      lastNames: ["Popescu", "Ionescu", "Rusu", "Ceban", "Moraru", "Rotaru", "Ciobanu", "Grosu"]
    }
  };

  const countryData = namesByCountry[country] || namesByCountry.Thailand;
  if (!countryData) {
    return { firstName: "Worker", lastName: "Name" };
  }
  
  return {
    firstName: countryData.firstNames[Math.floor(Math.random() * countryData.firstNames.length)] || "Worker",
    lastName: countryData.lastNames[Math.floor(Math.random() * countryData.lastNames.length)] || "Name"
  };
};

const generateNationalId = (country: string) => {
  return `${country.substring(0, 2).toUpperCase()}${randomNumericString(9)}`;
};

const generateVisaNumber = (country: string) => {
  const countryCode = country.substring(0, 2).toUpperCase();
  return `B1-${countryCode}${randomNumericString(6)}`;
};

const generateIsraeliAddress = () => {
  const cities = ["תל אביב", "ירושלים", "חיפה", "באר שבע", "אשדוד", "נתניה", "ראשון לציון", "פתח תקווה"];
  const streets = ["הרצל", "בן גוריון", "ויצמן", "ביאליק", "רוטשילד", "אלנבי", "דיזנגוף", "ז'בוטינסקי"];

  return {
    street: `${streets[Math.floor(Math.random() * streets.length)]} ${Math.floor(Math.random() * 100) + 1}`,
    city: cities[Math.floor(Math.random() * cities.length)],
    country: "ישראל",
    zipCode: `${Math.floor(Math.random() * 9) + 1}${randomNumericString(6)}`
  };
};

async function main() {
  const prisma = new PrismaClient();

  try {
    console.log("🚀 Starting enhanced seed with comprehensive test scenarios...");

    // Delete all data
    console.log("🗑️  Clearing existing data...");
    await prisma.payslipItem.deleteMany();
    await prisma.payslip.deleteMany();
    await prisma.alert.deleteMany();
    await prisma.form101.deleteMany();
    await prisma.bankAccount.deleteMany();
    await prisma.association.deleteMany();
    await prisma.salaryRecord.deleteMany();
    await prisma.salaryTemplateComponent.deleteMany();
    await prisma.employee.deleteMany();
    await prisma.deductionComponent.deleteMany();
    await prisma.paymentComponent.deleteMany();
    await prisma.valueComponent.deleteMany();
    await prisma.formula.deleteMany();
    await prisma.salaryTemplate.deleteMany();
    await prisma.employeeRole.deleteMany();
    await prisma.department.deleteMany();
    await prisma.user.deleteMany();
    await prisma.employer.deleteMany();
    await prisma.tenant.deleteMany();

    // Create basic structure
    const password = "*********";
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create tenant
    const defaultTenant = await prisma.tenant.create({
      data: {
        name: "Torgman HR",
        plan: "ENTERPRISE",
      }
    });
    
    // Create employer
    const torgmanEmployer = await prisma.employer.create({
      data: {
        name: "אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע\"מ",
        identifier: "*********",
        companyId: "*********",
        tenantId: defaultTenant.id,
        taxId: "*********",
        niNumber: "*********",
        industry: Sector.CONSTRUCTION,
        payrollDay: 10
      }
    });
    
    // Create employer address
    await prisma.employerAddress.create({
      data: {
        employerId: torgmanEmployer.id,
        street: "רחוב התעשייה", 
        houseNumber: "10",
        city: "באר שבע",
        country: "ישראל",
        postalCode: "7670000",
        isMain: true
      }
    });
    
    // Create employer contact
    await prisma.employerContact.create({
      data: {
        employerId: torgmanEmployer.id,
        contactPerson: "אברהם טורגמן",
        email: "<EMAIL>",
        primaryPhone: "050-2466626",
        position: "מנכ\"ל"
      }
    });

    // Create multiple departments
    const constructionDept = await prisma.department.create({
      data: {
        name: "עובדי בניין זרים",
        code: "CONST",
        employerId: torgmanEmployer.id,
        tenantId: defaultTenant.id,
        description: "מחלקת עובדי בניין זרים",
        isActive: true
      }
    });

    const plasteringDept = await prisma.department.create({
      data: {
        name: "עובדי טיח וגבס",
        code: "PLASTER",
        employerId: torgmanEmployer.id,
        tenantId: defaultTenant.id,
        description: "מחלקת עובדי טיח וגבס",
        isActive: true
      }
    });

    const electricDept = await prisma.department.create({
      data: {
        name: "חשמלאים",
        code: "ELECTRIC",
        employerId: torgmanEmployer.id,
        tenantId: defaultTenant.id,
        description: "מחלקת חשמלאים",
        isActive: true
      }
    });

    // Create employee roles with different skill levels
    const juniorWorkerRole = await prisma.employeeRole.create({
      data: {
        name: "פועל בניין מתחיל",
        description: "פועל בניין ללא ניסיון או עם ניסיון מועט",
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        isActive: true
      }
    });

    const seniorWorkerRole = await prisma.employeeRole.create({
      data: {
        name: "פועל בניין מקצועי",
        description: "פועל בניין מקצועי עם ניסיון",
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        isActive: true
      }
    });

    const foremanRole = await prisma.employeeRole.create({
      data: {
        name: "ראש צוות",
        description: "מנהל צוות עבודה",
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        isActive: true
      }
    });

    // Create comprehensive deduction components
    console.log("💸 Creating deduction components...");

    const incomeTaxDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "מס הכנסה",
        description: "ניכוי מס הכנסה",
        code: "TAX",
        deductionType: DeductionType.INCOME_TAX,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    const nationalInsuranceDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "ביטוח לאומי",
        description: "ניכוי ביטוח לאומי",
        code: "NI",
        deductionType: DeductionType.NATIONAL_INSURANCE,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        isActive: true
      }
    });

    const pensionEmployeeDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "פנסיה - עובד",
        description: "הפרשת עובד לקרן פנסיה",
        code: "PENSION_EMP",
        deductionType: DeductionType.PENSION,
        percentageOfSalary: 6.0,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: true,
        isActive: true
      }
    });

    const pensionEmployerDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "פנסיה - מעסיק",
        description: "הפרשת מעסיק לקרן פנסיה",
        code: "PENSION_EMPR",
        deductionType: DeductionType.PENSION,
        percentageOfSalary: 6.5,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: true,
        isActive: true
      }
    });

    const educationFundDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "קרן השתלמות",
        description: "הפרשה לקרן השתלמות",
        code: "EDUC_FUND",
        deductionType: DeductionType.EDUCATION_FUND,
        percentageOfSalary: 2.5,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    const housingDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "ניכוי דיור",
        description: "ניכוי עבור מגורי עובדים זרים",
        code: "HOUSING",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    // Additional deduction components
    const advancePaymentDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "החזר מקדמה",
        description: "ניכוי החזר מקדמת שכר",
        code: "ADVANCE_PAYMENT",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    const loanRepaymentDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "החזר הלוואה",
        description: "ניכוי החזר הלוואה מהמעסיק",
        code: "LOAN_REPAYMENT",
        deductionType: DeductionType.LOAN_REPAYMENT,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    const fineDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "קנס",
        description: "ניכוי קנס משמעתי",
        code: "FINE",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    const unionFeesDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "דמי חבר ועד",
        description: "ניכוי דמי חבר לועד עובדים",
        code: "UNION_FEES",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    const absenceDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "ניכוי היעדרות",
        description: "ניכוי עבור ימי היעדרות",
        code: "ABSENCE",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    const medicalInsuranceDeduction = await prisma.deductionComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "ביטוח רפואי פרטי",
        description: "ניכוי עבור ביטוח רפואי פרטי",
        code: "MEDICAL_INSURANCE",
        deductionType: DeductionType.HEALTH_INSURANCE,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    // Create diverse payment components
    console.log("💰 Creating payment components...");

    const basePayment = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "שכר בסיס",
        description: "שכר בסיס לשעה",
        code: "BASE",
        paymentType: PaymentType.ALLOWANCE,
        isActive: true
      }
    });

    const seniorityBonus = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "תוספת ותק",
        description: "תוספת שכר על בסיס ותק",
        code: "SENIORITY",
        paymentType: PaymentType.ALLOWANCE,
        isActive: true
      }
    });

    const skillBonus = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "תוספת מקצועיות",
        description: "תוספת עבור עובדים מקצועיים",
        code: "SKILL",
        paymentType: PaymentType.ALLOWANCE,
        isActive: true
      }
    });

    const transportAllowance = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "דמי נסיעות",
        description: "החזר נסיעות",
        code: "TRANSPORT",
        paymentType: PaymentType.EXPENSE,
        isActive: true
      }
    });

    const mealAllowance = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "דמי ארוחות",
        description: "תשלום עבור ארוחות",
        code: "MEALS",
        paymentType: PaymentType.EXPENSE,
        isActive: true
      }
    });

    const toolsAllowance = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "החזר כלי עבודה",
        description: "החזר עבור רכישת כלי עבודה",
        code: "TOOLS",
        paymentType: PaymentType.REIMBURSEMENT,
        isActive: true
      }
    });

    // Additional payment components based on Excel table
    console.log("💰 Creating additional payment components from Excel table...");

    // Overtime 125% - שעות נוספות 125%
    const overtime125Payment = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "שעות נוספות 125%",
        description: "תשלום שעות נוספות בתעריף 125%",
        code: "OVERTIME_125",
        paymentType: PaymentType.OVERTIME,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Overtime 150% - שעות נוספות 150%
    const overtime150Payment = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "שעות נוספות 150%",
        description: "תשלום שעות נוספות בתעריף 150%",
        code: "OVERTIME_150",
        paymentType: PaymentType.OVERTIME,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Overtime 200% - שעות נוספות 200%
    const overtime200Payment = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "שעות נוספות 200%",
        description: "תשלום שעות נוספות בתעריף 200%",
        code: "OVERTIME_200",
        paymentType: PaymentType.OVERTIME,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Night shift allowance - תוספת עבודת לילה
    const nightShiftAllowance = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "תוספת עבודת לילה",
        description: "תוספת עבור עבודה במשמרת לילה",
        code: "NIGHT_SHIFT",
        paymentType: PaymentType.ALLOWANCE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Vacation pay - דמי הבראה
    const vacationPay = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "דמי הבראה",
        description: "תשלום דמי הבראה שנתיים",
        code: "VACATION_PAY",
        paymentType: PaymentType.ALLOWANCE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Sick leave pay - דמי מחלה
    const sickLeavePay = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "דמי מחלה",
        description: "תשלום עבור ימי מחלה",
        code: "SICK_LEAVE",
        paymentType: PaymentType.ALLOWANCE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Holiday pay - דמי חג
    const holidayPay = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "דמי חג",
        description: "תשלום עבור ימי חג",
        code: "HOLIDAY_PAY",
        paymentType: PaymentType.ALLOWANCE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Clothing allowance - דמי ביגוד
    const clothingAllowance = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "דמי ביגוד",
        description: "קצובת ביגוד שנתית",
        code: "CLOTHING",
        paymentType: PaymentType.ALLOWANCE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Work from home allowance - תשלום עבודה מהבית
    const workFromHomeAllowance = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "החזר עבודה מהבית",
        description: "החזר הוצאות עבודה מהבית",
        code: "WFH_ALLOWANCE",
        paymentType: PaymentType.EXPENSE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        isActive: true
      }
    });

    // One-time bonus - בונוס חד פעמי
    const oneTimeBonus = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "בונוס חד פעמי",
        description: "בונוס חד פעמי",
        code: "ONE_TIME_BONUS",
        paymentType: PaymentType.ALLOWANCE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isOneTime: true,
        isActive: true
      }
    });

    // Annual bonus - בונוס שנתי
    const annualBonus = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "בונוס שנתי",
        description: "בונוס שנתי/13 משכורת",
        code: "ANNUAL_BONUS",
        paymentType: PaymentType.ALLOWANCE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: true,
        isActive: true
      }
    });

    // Gift value - שווי מתנה
    const giftValue = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "שווי מתנה",
        description: "שווי מתנות לעובדים",
        code: "GIFT_VALUE",
        paymentType: PaymentType.OTHER,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    // Create informative components (אינפורמטיבי)
    console.log("ℹ️ Creating informative components...");

    // Absence days - ימי היעדרות
    const absenceDays = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "ימי היעדרות",
        description: "ימי היעדרות ללא תשלום",
        code: "ABSENCE_DAYS",
        paymentType: PaymentType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    // Sick days used - ימי מחלה שנוצלו
    const sickDaysUsed = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "ימי מחלה שנוצלו",
        description: "מספר ימי מחלה שנוצלו",
        code: "SICK_DAYS_USED",
        paymentType: PaymentType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    // Vacation days used - ימי חופשה שנוצלו
    const vacationDaysUsed = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "ימי חופשה שנוצלו",
        description: "מספר ימי חופשה שנוצלו",
        code: "VACATION_DAYS_USED",
        paymentType: PaymentType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    // Hours bank balance - יתרת בנק שעות
    const hoursBankBalance = await prisma.paymentComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "יתרת בנק שעות",
        description: "יתרת שעות בבנק שעות",
        code: "HOURS_BANK_BALANCE",
        paymentType: PaymentType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isActive: true
      }
    });

    // Create value components
    console.log("💵 Creating value components...");
    
    const mealValueComponent = await prisma.valueComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        code: "MEAL_VALUE",
        name: "ערך ארוחה",
        description: "ערך ארוחה יומית",
        valueComponentType: ValueComponentType.MEAL,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isOneTime: false,
        isActive: true
      }
    });
    
    const phoneValueComponent = await prisma.valueComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        code: "PHONE_VALUE",
        name: "שווי טלפון",
        description: "שווי שימוש בטלפון חברה",
        valueComponentType: ValueComponentType.PHONE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        isOneTime: false,
        isActive: true
      }
    });

    const carValueComponent = await prisma.valueComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        code: "CAR_VALUE",
        name: "שווי רכב",
        description: "שווי שימוש ברכב חברה",
        valueComponentType: ValueComponentType.VEHICLE,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        isOneTime: false,
        isActive: true
      }
    });

    const overtimeValueComponent = await prisma.valueComponent.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        code: "OVERTIME_THRESHOLD",
        name: "סף שעות נוספות",
        description: "מספר שעות עבודה רגילות לפני שעות נוספות",
        valueComponentType: ValueComponentType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        isOneTime: false,
        isActive: true
      }
    });
    
    // Create formulas
    console.log("📊 Creating salary calculation formulas...");
    
    // Progressive tax formula for Israeli employees
    const israeliTaxFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב מס הכנסה מדורג לישראלים",
        description: "נוסחת חישוב מס הכנסה מדורג לפי מדרגות מס 2025",
        type: FormulaType.TAX,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: `
          const brackets = ${JSON.stringify(TAX_BRACKETS_2025)};
          let tax = 0;
          let prevTo = 0;
          for (const bracket of brackets) {
            if ({baseSalary} > bracket.from) {
              const taxableInBracket = Math.min({baseSalary}, bracket.to) - prevTo;
              tax += taxableInBracket * bracket.rate;
              prevTo = bracket.to;
            }
          }
          return tax - ({creditPoints} * ${TAX_CREDITS_2025.CREDIT_POINT_VALUE});
        `
      }
    });
    
    const foreignWorkerTaxFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב מס לעובד זר",
        description: "נוסחת חישוב מס הכנסה לעובדים זרים - 14% אחיד",
        type: FormulaType.TAX,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: "{baseSalary} * 0.14"
      }
    });
    
    // Social Security formulas for different employee types
    const foreignWorkerSocialSecurityFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב ביטוח לאומי לעובד זר",
        description: "נוסחת חישוב ביטוח לאומי לעובדים זרים לפי תקרות 2025",
        type: FormulaType.DEDUCTION,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: `
          const reducedCeiling = ${SOCIAL_SECURITY_THRESHOLDS_2025.REDUCED_RATE_CEILING};
          const fullCeiling = ${SOCIAL_SECURITY_THRESHOLDS_2025.FULL_RATE_CEILING};
          const rates = ${JSON.stringify(SOCIAL_SECURITY_RATES_2025.FOREIGN_WORKER)};
          
          let socialSecurity = 0;
          if ({baseSalary} <= reducedCeiling) {
            socialSecurity = {baseSalary} * rates.reduced.employee;
          } else if ({baseSalary} <= fullCeiling) {
            socialSecurity = reducedCeiling * rates.reduced.employee + 
                           ({baseSalary} - reducedCeiling) * rates.full.employee;
          } else {
            socialSecurity = reducedCeiling * rates.reduced.employee + 
                           (fullCeiling - reducedCeiling) * rates.full.employee;
          }
          return socialSecurity;
        `
      }
    });
    
    const healthInsuranceFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב ביטוח בריאות",
        description: "נוסחת חישוב ביטוח בריאות לעובדים זרים - 3.1% עד תקרה",
        type: FormulaType.DEDUCTION,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: `Math.min({baseSalary} * 0.031, ${SOCIAL_SECURITY_THRESHOLDS_2025.HEALTH_INSURANCE_MAX})`
      }
    });

    const seniorityBonusFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב תוספת ותק",
        description: "2% לכל שנת ותק עד 10%",
        type: FormulaType.OTHER,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: "{baseSalary} * Math.min({yearsOfService} * 0.02, 0.10)"
      }
    });

    const overtimeFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב שעות נוספות",
        description: "חישוב תשלום שעות נוספות לפי חוק",
        type: FormulaType.OTHER,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: "({overtime125} * {hourlyRate} * 1.25) + ({overtime150} * {hourlyRate} * 1.5) + ({overtime175} * {hourlyRate} * 1.75) + ({overtime200} * {hourlyRate} * 2.0)"
      }
    });

    const transportFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב דמי נסיעות",
        description: "26.40 ₪ ליום עבודה",
        type: FormulaType.OTHER,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: "{workDays} * 26.40"
      }
    });

    // Employer social security contribution formula
    const employerSocialSecurityFormula = await prisma.formula.create({
      data: {
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        name: "חישוב ביטוח לאומי מעסיק - עובד זר",
        description: "נוסחת חישוב ביטוח לאומי חלק מעסיק לעובדים זרים",
        type: FormulaType.OTHER,
        startDate: new Date(2025, 0, 1),
        status: FormulaStatus.ACTIVE,
        formulaCode: `
          const reducedCeiling = ${SOCIAL_SECURITY_THRESHOLDS_2025.REDUCED_RATE_CEILING};
          const fullCeiling = ${SOCIAL_SECURITY_THRESHOLDS_2025.FULL_RATE_CEILING};
          const rates = ${JSON.stringify(SOCIAL_SECURITY_RATES_2025.FOREIGN_WORKER)};
          
          let employerContribution = 0;
          if ({baseSalary} <= reducedCeiling) {
            employerContribution = {baseSalary} * rates.reduced.employer;
          } else if ({baseSalary} <= fullCeiling) {
            employerContribution = reducedCeiling * rates.reduced.employer + 
                                 ({baseSalary} - reducedCeiling) * rates.full.employer;
          } else {
            employerContribution = reducedCeiling * rates.reduced.employer + 
                                 (fullCeiling - reducedCeiling) * rates.full.employer;
          }
          return employerContribution;
        `
      }
    });

    // Create salary templates with different configurations
    console.log("📋 Creating salary templates...");

    const juniorWorkerTemplate = await prisma.salaryTemplate.create({
      data: {
        name: "תבנית שכר פועל מתחיל",
        description: "תבנית שכר לפועל בניין מתחיל",
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        isActive: true,        
        components: {
          baseSalary: {
            type: "HOURLY",
            rate: MIN_HOURLY_WAGE,
            currency: "ILS"
          },
          overtime: {
            type: "PERCENTAGE",
            rate: 125,
            baseRate: MIN_HOURLY_WAGE
          }
        }
      }
    });

    const seniorWorkerTemplate = await prisma.salaryTemplate.create({
      data: {
        name: "תבנית שכר פועל מקצועי",
        description: "תבנית שכר לפועל בניין מקצועי עם תוספות",
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        isActive: true,        
        components: {
          baseSalary: {
            type: "HOURLY",
            rate: MIN_HOURLY_WAGE * 1.15, // 15% more than minimum
            currency: "ILS"
          },
          overtime: {
            type: "PERCENTAGE",
            rate: 125,
            baseRate: MIN_HOURLY_WAGE * 1.15
          }
        }
      }
    });

    const foremanTemplate = await prisma.salaryTemplate.create({
      data: {
        name: "תבנית שכר ראש צוות",
        description: "תבנית שכר לראש צוות עם שכר חודשי",
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        isActive: true,        
        components: {
          baseSalary: {
            type: "MONTHLY",
            amount: MIN_MONTHLY_SALARY * 1.5,
            currency: "ILS"
          }
        }
      }
    });

    // Create template components
    console.log("🔧 Creating salary template components...");

    // Junior worker template components
    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: juniorWorkerTemplate.id,
        componentCode: "BASE",
        componentName: "שכר בסיס",
        componentType: "earning",
        calculationType: "fixed",
        fixedAmount: MIN_HOURLY_WAGE,
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: juniorWorkerTemplate.id,
        componentCode: "TRANSPORT",
        componentName: "דמי נסיעות",
        componentType: "earning",
        calculationType: "formula",
        formula: "{workDays} * 26.40",
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    // Senior worker template components
    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: seniorWorkerTemplate.id,
        componentCode: "BASE",
        componentName: "שכר בסיס",
        componentType: "earning",
        calculationType: "fixed",
        fixedAmount: MIN_HOURLY_WAGE * 1.15,
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: seniorWorkerTemplate.id,
        componentCode: "SKILL",
        componentName: "תוספת מקצועיות",
        componentType: "earning",
        calculationType: "percentage",
        percentage: 10,
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: seniorWorkerTemplate.id,
        componentCode: "TRANSPORT",
        componentName: "דמי נסיעות",
        componentType: "earning",
        calculationType: "formula",
        formula: "{workDays} * 26.40",
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: seniorWorkerTemplate.id,
        componentCode: "SENIORITY",
        componentName: "תוספת ותק",
        componentType: "earning",
        calculationType: "formula",
        formula: "{baseSalary} * Math.min({yearsOfService} * 0.02, 0.10)",
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    // Foreman template components
    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: foremanTemplate.id,
        componentCode: "BASE",
        componentName: "משכורת חודשית",
        componentType: "earning",
        calculationType: "fixed",
        fixedAmount: MIN_MONTHLY_SALARY * 1.5,
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: foremanTemplate.id,
        componentCode: "PHONE_VALUE",
        componentName: "שווי טלפון",
        componentType: "earning",
        calculationType: "fixed",
        fixedAmount: 100,
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    await prisma.salaryTemplateComponent.create({
      data: {
        templateId: foremanTemplate.id,
        componentCode: "CAR_VALUE",
        componentName: "שווי רכב",
        componentType: "earning",
        calculationType: "fixed",
        fixedAmount: 2500,
        isActive: true,
        effectiveFrom: new Date(2025, 0, 1)
      }
    });

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "אברהם טורגמן",
        password: hashedPassword,
        role: Role.ADMIN,
        tenantId: defaultTenant.id,
        employerId: torgmanEmployer.id,
        isActive: true,
      }
    });

    console.log("✅ Basic structure created");
    
    // Create system settings for tax and social security
    console.log("⚙️ Creating system settings...");
    
    await prisma.setting.create({
      data: {
        tenantId: defaultTenant.id,
        key: "TAX_BRACKETS_2025",
        value: TAX_BRACKETS_2025,
        description: "מדרגות מס הכנסה לשנת 2025"
      }
    });
    
    await prisma.setting.create({
      data: {
        tenantId: defaultTenant.id,
        key: "SOCIAL_SECURITY_RATES_2025",
        value: SOCIAL_SECURITY_RATES_2025,
        description: "שיעורי ביטוח לאומי לפי סוגי עובדים 2025"
      }
    });
    
    await prisma.setting.create({
      data: {
        tenantId: defaultTenant.id,
        key: "SOCIAL_SECURITY_THRESHOLDS_2025",
        value: SOCIAL_SECURITY_THRESHOLDS_2025,
        description: "תקרות ביטוח לאומי 2025"
      }
    });
    
    await prisma.setting.create({
      data: {
        tenantId: defaultTenant.id,
        key: "TAX_CREDITS_2025",
        value: TAX_CREDITS_2025,
        description: "נקודות זיכוי ותקרות מס 2025"
      }
    });
    
    await prisma.setting.create({
      data: {
        tenantId: defaultTenant.id,
        key: "TRANSPORT_ALLOWANCE_2025",
        value: { dailyAmount: 26.40, currency: "ILS" },
        description: "דמי נסיעות יומיים 2025"
      }
    });
    
    await prisma.setting.create({
      data: {
        tenantId: defaultTenant.id,
        key: "VACATION_PAY_RATES_2025",
        value: {
          days: [
            { yearsOfService: 1, days: 5 },
            { yearsOfService: 2, days: 6 },
            { yearsOfService: 3, days: 7 },
            { yearsOfService: 4, days: 8 },
            { yearsOfService: 10, days: 10 }
          ],
          dailyRate: 378 // תעריף יומי לדמי הבראה
        },
        description: "טבלת דמי הבראה 2025"
      }
    });
    
    // Create attendance agreements
    console.log("📝 Creating attendance agreements...");
    
    const basicAgreement = await prisma.attendanceAgreement.create({
      data: {
        tenantId: defaultTenant.id,
        code: "BASIC_CONST",
        name: "הסכם בסיסי לעובדי בניין",
        description: "הסכם עבודה בסיסי לעובדי בניין זרים",
        status: AgreementStatus.ACTIVE,
        workDaysPerWeek: 6,
        hoursPerDay: 9,
        monthlyHours: MONTHLY_HOURS,
        overtimeThreshold: 9,
        nightShiftStart: "23:00",
        nightShiftEnd: "06:00"
      }
    });

    const seniorAgreement = await prisma.attendanceAgreement.create({
      data: {
        tenantId: defaultTenant.id,
        code: "SENIOR_CONST",
        name: "הסכם לעובדים מקצועיים",
        description: "הסכם עבודה לעובדים מקצועיים עם תנאים משופרים",
        status: AgreementStatus.ACTIVE,
        workDaysPerWeek: 6,
        hoursPerDay: 8,
        monthlyHours: 171.4,
        overtimeThreshold: 8,
        nightShiftStart: "22:00",
        nightShiftEnd: "06:00"
      }
    });

    // Create diverse employees with different scenarios
    console.log("👷 Creating 20 diverse foreign workers...");

    const employees = [];
    const countries = ["Thailand", "Philippines", "China", "Moldova"];
    const sectors = [Sector.CONSTRUCTION, Sector.AGRICULTURE, Sector.INDUSTRY];
    const departments = [constructionDept, plasteringDept, electricDept];
    const roles = [juniorWorkerRole, seniorWorkerRole, foremanRole];
    const templates = [juniorWorkerTemplate, seniorWorkerTemplate, foremanTemplate];
    const agreements = [basicAgreement, seniorAgreement];

    for (let i = 0; i < 20; i++) {
      const country = countries[i % countries.length]!;
      const sector = sectors[i % sectors.length]!;
      const department = departments[i % departments.length]!;
      const role = roles[i % roles.length]!;
      const template = templates[i % templates.length]!;
      const agreement = agreements[i % agreements.length]!;
      
      const nameData = generateWorkerName(country);
      const israeliAddress = generateIsraeliAddress();
      const startDate = randomPastDate(3);

      // Generate diverse visa situations
      let visaExpiryDate: Date;
      const randomCase = Math.random();
      if (randomCase < 0.05) {
        // 5% expired visas
        visaExpiryDate = randomPastDate(0.5);
      } else if (randomCase < 0.15) {
        // 10% expiring within 30 days
        visaExpiryDate = new Date();
        visaExpiryDate.setDate(visaExpiryDate.getDate() + randomInt(1, 30));
      } else if (randomCase < 0.25) {
        // 10% expiring within 90 days
        visaExpiryDate = new Date();
        visaExpiryDate.setDate(visaExpiryDate.getDate() + randomInt(31, 90));
      } else {
        // 75% valid visas
        visaExpiryDate = randomFutureDate(randomInt(1, 3));
      }

      // Diverse marital status
      const maritalStatuses = [MaritalStatus.MARRIED, MaritalStatus.SINGLE, MaritalStatus.DIVORCED, MaritalStatus.WIDOWED];
      const randomMaritalStatus = maritalStatuses[Math.floor(Math.random() * maritalStatuses.length)]!;
      
      // Calculate appropriate salary based on role
      let baseSalary = MIN_MONTHLY_SALARY;
      let hourlyRate = MIN_HOURLY_WAGE;
      let basis: Basis = Basis.HOURLY;
      
      if (role.id === seniorWorkerRole.id) {
        hourlyRate = MIN_HOURLY_WAGE * 1.15;
        baseSalary = MIN_MONTHLY_SALARY * 1.15;
      } else if (role.id === foremanRole.id) {
        baseSalary = MIN_MONTHLY_SALARY * 1.5;
        hourlyRate = baseSalary / MONTHLY_HOURS;
        basis = Basis.MONTHLY;
      }
      
      const employee = await prisma.employee.create({
        data: {
          tenantId: defaultTenant.id,
          employerId: torgmanEmployer.id,
          departmentId: department.id,
          firstName: nameData.firstName,
          lastName: nameData.lastName,
          nationalId: generateNationalId(country),
          birthDate: randomDate(new Date(1970, 0, 1), new Date(2000, 0, 1)),
          startDate: startDate,
          isForeign: true,
          country: country,
          sector: sector,
          agreementType: AgreementType.PERSONAL,
          visaNumber: generateVisaNumber(country),
          visaType: "B1",
          visaExpiry: visaExpiryDate,
          baseSalary: baseSalary
        }
      });
      
      // Create employee contact
      await prisma.employeeContact.create({
        data: {
          employeeId: employee.id,
          email: `${nameData.firstName.toLowerCase()}.${nameData.lastName.toLowerCase()}@worker.temp`,
          mobilePhone: `050-${randomNumericString(7)}`,
          emergencyContact: `${nameData.firstName} Family`,
          emergencyPhone: `+${country === "Thailand" ? "66" : country === "Philippines" ? "63" : country === "China" ? "86" : "373"}-${randomNumericString(9)}`,
          emergencyRelation: "FAMILY"
        }
      });
      
      // Create employee address
      await prisma.employeeAddress.create({
        data: {
          employeeId: employee.id,
          street: israeliAddress.street,
          city: israeliAddress.city,
          country: israeliAddress.country,
          postalCode: israeliAddress.zipCode,
          isMain: true
        }
      });

      employees.push(employee);

      // Create associations
      // Role association
      await prisma.association.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          associationType: AssociationType.ROLE,
          roleId: role.id,
          startDate: startDate,
          notes: `תפקיד ${role.name} עבור ${nameData.firstName} ${nameData.lastName}`,
        }
      });

      // Department association
      await prisma.association.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          associationType: AssociationType.DEPARTMENT,
          departmentId: department.id,
          startDate: startDate,
          notes: `שיוך למחלקת ${department.name}`,
        }
      });

      // Salary template association
      await prisma.association.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          associationType: AssociationType.SALARY_TEMPLATE,
          salaryTemplateId: template.id,
          startDate: startDate,
          notes: `תבנית שכר ${template.name}`,
        }
      });

      // Create salary record
      await prisma.salaryRecord.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          basis: basis,
          currency: Currency.ILS,
          payFrequency: PayFrequency.MONTHLY,
          amount: baseSalary,
          effectiveFrom: startDate,
          hourlyRate: hourlyRate,
          standardMonthlyHours: MONTHLY_HOURS,
          standardDailyHours: agreement.hoursPerDay,
          workDaysPerWeek: agreement.workDaysPerWeek,
        }
      });

      // Create value component values for specific employees
      if (role.id === foremanRole.id) {
        // Phone value for foremen
        // Using Employee.create with nested valueComponentValues relation
        // This is handled through the association system now
      }

      // For now, skip creating ValueComponentValue records as this model doesn't exist
      // The value components are used through associations and calculations

      // Create bank account for most employees
      if (Math.random() > 0.2) { // 80% have bank accounts
        const banks = ["בנק הפועלים", "בנק לאומי", "בנק דיסקונט", "בנק מזרחי"];
        await prisma.bankAccount.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            bankName: banks[Math.floor(Math.random() * banks.length)]!,
            branchCode: randomNumericString(3),
            accountNumber: randomNumericString(7),
          }
        });
      }

      // Create Form 101 with various configurations
      if (Math.random() > 0.1) { // 90% have Form 101
        const childrenCount = randomMaritalStatus === MaritalStatus.SINGLE ? 0 : randomInt(0, 5);
        const spouseWorks = randomMaritalStatus === MaritalStatus.MARRIED && Math.random() > 0.3;
        
        await prisma.form101.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            taxYear: 2024,
            status: Math.random() > 0.15 ? Form101Status.SIGNED : Form101Status.DRAFT,
            maritalStatus: randomMaritalStatus,
            spouseWorks: spouseWorks,
            childrenCount: childrenCount,
            isMainEmployer: Math.random() > 0.9 ? false : true, // 10% have other employers
            additionalCreditPoints: Math.random() > 0.7 ? randomInt(1, 3) : null, // 30% have additional credits
            exemptionPercentage: Math.random() > 0.95 ? randomInt(10, 30) : null, // 5% have exemptions
          }
        });
      }

      // Create employee agreement
      await prisma.employeeAgreement.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          agreementId: agreement.id,
          startDate: employee.startDate,
          notes: `הסכם העסקה ${agreement.name} לעובד ${employee.firstName} ${employee.lastName}`
        }
      });

      if ((i + 1) % 5 === 0) {
        console.log(`   Created ${i + 1}/20 employees...`);
      }
    }

    console.log("✅ 20 diverse employees created");

    // Create movement types for attendance
    console.log("🕒 Creating attendance records...");
    
    const clockInMovementType = await prisma.movementType.create({
      data: {
        tenantId: defaultTenant.id,
        name: "כניסה לעבודה",
        code: "CLOCK_IN",
        category: MovementCategory.CHECK_IN,
        description: "תיעוד זמן הגעה לעבודה"
      }
    });
    
    const clockOutMovementType = await prisma.movementType.create({
      data: {
        tenantId: defaultTenant.id,
        name: "יציאה מהעבודה",
        code: "CLOCK_OUT",
        category: MovementCategory.CHECK_OUT,
        description: "תיעוד זמן יציאה מהעבודה"
      }
    });
    
    // Create locations
    const mainSite = await prisma.location.create({
      data: {
        tenantId: defaultTenant.id,
        code: "MAIN_SITE",
        name: "אתר בנייה מרכזי",
        description: "אתר הבנייה המרכזי של החברה",
        address: "רחוב הבנאים 10, תל אביב",
        isActive: true
      }
    });

    const secondarySite = await prisma.location.create({
      data: {
        tenantId: defaultTenant.id,
        code: "SECOND_SITE",
        name: "אתר בנייה משני",
        description: "אתר בנייה בירושלים",
        address: "רחוב הקבלנים 5, ירושלים",
        isActive: true
      }
    });
    
    // Create realistic attendance data with various scenarios
    const today = new Date();
    
    for (const employee of employees) {
      // Create attendance data for the last 30 working days
      for (let dayOffset = 30; dayOffset > 0; dayOffset--) {
        const workDate = new Date(today);
        workDate.setDate(today.getDate() - dayOffset);
        
        // Skip Saturdays
        if (workDate.getDay() === 6) continue;

        // Simulate various attendance scenarios
        const attendanceScenario = Math.random();
        
        if (attendanceScenario < 0.02) {
          // 2% absent - no attendance record
          continue;
        } else if (attendanceScenario < 0.05) {
          // 3% late arrival
          const clockInTime = new Date(workDate);
          clockInTime.setHours(8 + randomInt(1, 2), randomInt(0, 59), 0, 0); // Late (8:00-9:59)
          
          await prisma.movement.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              typeId: clockInMovementType.id,
              locationId: Math.random() > 0.8 ? secondarySite.id : mainSite.id,
              timestamp: clockInTime,
              source: MovementSource.MANUAL,
              isApproved: true
            }
          });
          
          const clockOutTime = new Date(workDate);
          clockOutTime.setHours(16 + randomInt(0, 1), randomInt(0, 59), 0, 0);
          
          await prisma.movement.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              typeId: clockOutMovementType.id,
              locationId: Math.random() > 0.8 ? secondarySite.id : mainSite.id,
              timestamp: clockOutTime,
              source: MovementSource.MANUAL,
              isApproved: true
            }
          });
        } else if (attendanceScenario < 0.15) {
          // 10% work overtime
          const clockInTime = new Date(workDate);
          clockInTime.setHours(6 + randomInt(0, 1), randomInt(30, 59), 0, 0); // Early (6:30-7:59)
          
          await prisma.movement.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              typeId: clockInMovementType.id,
              locationId: Math.random() > 0.8 ? secondarySite.id : mainSite.id,
              timestamp: clockInTime,
              source: MovementSource.MANUAL,
              isApproved: true
            }
          });
          
          const clockOutTime = new Date(workDate);
          clockOutTime.setHours(18 + randomInt(0, 2), randomInt(0, 59), 0, 0); // Late (18:00-20:59)
          
          await prisma.movement.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              typeId: clockOutMovementType.id,
              locationId: Math.random() > 0.8 ? secondarySite.id : mainSite.id,
              timestamp: clockOutTime,
              source: MovementSource.MANUAL,
              isApproved: true
            }
          });
        } else {
          // Normal attendance
          const clockInTime = new Date(workDate);
          clockInTime.setHours(7 + randomInt(0, 1), randomInt(0, 59), 0, 0); // Normal (7:00-8:59)
          
          await prisma.movement.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              typeId: clockInMovementType.id,
              locationId: Math.random() > 0.8 ? secondarySite.id : mainSite.id,
              timestamp: clockInTime,
              source: MovementSource.MANUAL,
              isApproved: true
            }
          });
          
          const clockOutTime = new Date(workDate);
          clockOutTime.setHours(16 + randomInt(0, 1), randomInt(0, 59), 0, 0); // Normal (16:00-17:59)
          
          await prisma.movement.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              typeId: clockOutMovementType.id,
              locationId: Math.random() > 0.8 ? secondarySite.id : mainSite.id,
              timestamp: clockOutTime,
              source: MovementSource.MANUAL,
              isApproved: true
            }
          });
        }
      }
    }
    
    console.log("✅ Created attendance records");

    // Generate salary transactions
    console.log("💸 Creating salary transactions...");

    let transactionCount = 0;
    for (const employee of employees.slice(0, 10)) { // Add transactions for first 10 employees
      // Add various types of transactions
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();

      // Bonus transaction
      if (Math.random() > 0.7) {
        await prisma.salaryTransaction.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            amount: randomInt(500, 2000),
            description: "בונוס חג",
            periodMonth: currentMonth,
            periodYear: currentYear,
            isProcessed: false
          }
        });
        transactionCount++;
      }

      // Tool reimbursement
      if (Math.random() > 0.8) {
        await prisma.salaryTransaction.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            amount: randomInt(200, 800),
            description: "החזר רכישת כלי עבודה",
            periodMonth: currentMonth,
            periodYear: currentYear,
            isProcessed: false
          }
        });
        transactionCount++;
      }

      // Deduction for damage
      if (Math.random() > 0.9) {
        await prisma.salaryTransaction.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            amount: -randomInt(100, 500),
            description: "ניכוי עבור נזק לציוד",
            periodMonth: currentMonth,
            periodYear: currentYear,
            isProcessed: false
          }
        });
        transactionCount++;
      }

      // Advance payment
      if (Math.random() > 0.85) {
        await prisma.salaryTransaction.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            amount: -randomInt(1000, 3000),
            description: "החזר מקדמה",
            periodMonth: currentMonth,
            periodYear: currentYear,
            isProcessed: false
          }
        });
        transactionCount++;
      }
    }

    console.log(`✅ Created ${transactionCount} salary transactions`);

    // Generate sample payslips with new calculation method
    console.log("💰 Generating comprehensive payslips...");

    const monthsToGenerate = 3;
    let payslipCount = 0;

    for (let monthOffset = 0; monthOffset < monthsToGenerate; monthOffset++) {
      const currentDate = new Date();
      currentDate.setMonth(currentDate.getMonth() - monthOffset);
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;

      for (const employee of employees.slice(0, 10)) { // Generate for first 10 employees
        try {
          // Use the calculatePayslip function from payslip service
          const workHours = {
            regularHours: 170 + randomInt(-10, 10),
            overtime125: Math.random() > 0.5 ? randomInt(0, 20) : 0,
            overtime150: Math.random() > 0.7 ? randomInt(0, 10) : 0,
            overtime175: Math.random() > 0.9 ? randomInt(0, 5) : 0,
            overtime200: 0
          };

          // Note: We'll call the service directly in the application
          // For seed, we'll create a simplified version
          const salaryRecord = await prisma.salaryRecord.findFirst({
            where: { employeeId: employee.id },
            orderBy: { effectiveFrom: 'desc' }
          });

          if (!salaryRecord) continue;

          const hourlyRate = Number(salaryRecord.hourlyRate || 0);
          const baseSalary = workHours.regularHours * hourlyRate;
          const overtime125Pay = workHours.overtime125 * hourlyRate * 1.25;
          const overtime150Pay = workHours.overtime150 * hourlyRate * 1.5;
          const grossSalary = baseSalary + overtime125Pay + overtime150Pay;

          const taxDeduction = grossSalary * 0.14;
          const socialSecurity = grossSalary * DEDUCTION_RATES.SOCIAL_SECURITY_EMPLOYEE;
          const healthInsurance = Math.min(grossSalary * DEDUCTION_RATES.HEALTH_INSURANCE, DEDUCTION_RATES.HEALTH_INSURANCE_MAX);
          const totalDeductions = taxDeduction + socialSecurity + healthInsurance;
          const netSalary = grossSalary - totalDeductions;

          const payslip = await prisma.payslip.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              periodStart: new Date(year, month - 1, 1),
              periodEnd: new Date(year, month, 0),
              status: monthOffset === 0 ? PayslipStatus.CALCULATED : PayslipStatus.APPROVED,
              grossPay: grossSalary,
              netPay: netSalary,
              currency: Currency.ILS,
              year: year,
              month: month,
              taxDeducted: taxDeduction,
              insuranceDeducted: socialSecurity + healthInsurance,
              issuedAt: new Date(new Date(year, month, 0).setDate(10)),
            }
          });

          // Create detailed payslip items
          await prisma.payslipItem.createMany({
            data: [
              {
                payslipId: payslip.id,
                description: "שכר בסיס",
                amount: baseSalary,
                type: PayslipItemType.EARNING,
                kod: PayslipItemKod.K_1000,
                units: workHours.regularHours,
                rate: hourlyRate
              },
              ...(workHours.overtime125 > 0 ? [{
                payslipId: payslip.id,
                description: "שעות נוספות 125%",
                amount: overtime125Pay,
                type: PayslipItemType.EARNING,
                kod: PayslipItemKod.K_1020,
                units: workHours.overtime125,
                rate: hourlyRate * 1.25
              }] : []),
              ...(workHours.overtime150 > 0 ? [{
                payslipId: payslip.id,
                description: "שעות נוספות 150%",
                amount: overtime150Pay,
                type: PayslipItemType.EARNING,
                kod: PayslipItemKod.K_1021,
                units: workHours.overtime150,
                rate: hourlyRate * 1.5
              }] : []),
              {
                payslipId: payslip.id,
                description: "מס הכנסה",
                amount: -taxDeduction,
                type: PayslipItemType.DEDUCTION,
                kod: PayslipItemKod.K_TAX
              },
              {
                payslipId: payslip.id,
                description: "ביטוח לאומי עובד",
                amount: -socialSecurity,
                type: PayslipItemType.DEDUCTION,
                kod: PayslipItemKod.K_NI_EMP
              },
              {
                payslipId: payslip.id,
                description: "ביטוח בריאות",
                amount: -healthInsurance,
                type: PayslipItemType.DEDUCTION,
                kod: PayslipItemKod.K_NI_HEALTH
              }
            ]
          });

          payslipCount++;
        } catch (error) {
          console.error(`Failed to create payslip for employee ${employee.id}:`, error);
        }
      }
    }

    console.log(`✅ Generated ${payslipCount} comprehensive payslips`);

    // Generate diverse compliance alerts
    console.log("🚨 Generating comprehensive compliance alerts...");

    let alertCount = 0;
    const currentDate = new Date();

    // Visa expiry alerts
    for (const employee of employees) {
      if (employee.visaExpiry) {
        const daysUntilExpiry = Math.ceil((employee.visaExpiry.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));

        if (daysUntilExpiry < 0) {
          // Expired visa - CRITICAL
          await prisma.alert.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              type: AlertType.CRITICAL,
              category: AlertCategory.VISA_EXPIRATION,
              severity: "CRITICAL",
              message: `ויזת העובד ${employee.firstName} ${employee.lastName} פגה ב-${employee.visaExpiry.toLocaleDateString('he-IL')} - יש לטפל מיידית!`,
              isRead: false,
              isResolved: false,
              dueDate: employee.visaExpiry,
            }
          });
          alertCount++;
        } else if (daysUntilExpiry <= 30) {
          // Expiring within 30 days - HIGH
          await prisma.alert.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              type: AlertType.WARNING,
              category: AlertCategory.VISA_EXPIRATION,
              severity: "HIGH",
              message: `ויזת העובד ${employee.firstName} ${employee.lastName} תפוג ב-${employee.visaExpiry.toLocaleDateString('he-IL')} (עוד ${daysUntilExpiry} ימים)`,
              isRead: false,
              isResolved: false,
              dueDate: employee.visaExpiry,
            }
          });
          alertCount++;
        } else if (daysUntilExpiry <= 90) {
          // Expiring within 90 days - MEDIUM
          await prisma.alert.create({
            data: {
              tenantId: defaultTenant.id,
              employeeId: employee.id,
              type: AlertType.INFO,
              category: AlertCategory.VISA_EXPIRATION,
              severity: "MEDIUM",
              message: `ויזת העובד ${employee.firstName} ${employee.lastName} תפוג ב-${employee.visaExpiry.toLocaleDateString('he-IL')} (עוד ${daysUntilExpiry} ימים)`,
              isRead: false,
              isResolved: false,
              dueDate: employee.visaExpiry,
            }
          });
          alertCount++;
        }
      }
    }

    // Missing Form 101 alerts
    const employeesWithoutForm101 = await prisma.employee.findMany({
      where: {
        tenantId: defaultTenant.id,
        form101s: { none: {} }
      }
    });

    for (const employee of employeesWithoutForm101) {
      await prisma.alert.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          type: AlertType.CRITICAL,
          category: AlertCategory.MISSING_FORM101,
          severity: "HIGH",
          message: `לעובד ${employee.firstName} ${employee.lastName} חסר טופס 101 - נדרשת חתימה`,
          isRead: false,
          isResolved: false,
        }
      });
      alertCount++;
    }

    // Missing bank account alerts
    const employeesWithoutBank = await prisma.employee.findMany({
      where: {
        tenantId: defaultTenant.id,
        bankAccounts: { none: {} }
      }
    });

    for (const employee of employeesWithoutBank) {
      await prisma.alert.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          type: AlertType.WARNING,
          category: AlertCategory.MISSING_DOCUMENTS,
          severity: "MEDIUM",
          message: `לעובד ${employee.firstName} ${employee.lastName} חסרים פרטי חשבון בנק`,
          isRead: false,
          isResolved: false,
        }
      });
      alertCount++;
    }

    // Draft Form 101 alerts
    const employeesWithDraftForm = await prisma.employee.findMany({
      where: {
        tenantId: defaultTenant.id,
        form101s: {
          some: {
            status: Form101Status.DRAFT
          }
        }
      }
    });

    for (const employee of employeesWithDraftForm) {
      await prisma.alert.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          type: AlertType.INFO,
          category: AlertCategory.MISSING_DOCUMENTS,
          severity: "LOW",
          message: `טופס 101 של ${employee.firstName} ${employee.lastName} בסטטוס טיוטה - נדרשת חתימה`,
          isRead: false,
          isResolved: false,
        }
      });
      alertCount++;
    }

    console.log(`✅ Generated ${alertCount} comprehensive compliance alerts`);

    console.log("\n🎉 Enhanced seed completed successfully!");
    console.log("📊 Summary:");
    console.log(`   • 1 Tenant: ${defaultTenant.name}`);
    console.log(`   • 1 Employer: ${torgmanEmployer.name}`);
    console.log(`   • 3 Departments`);
    console.log(`   • 3 Employee roles`);
    console.log(`   • 12 Deduction components (6 original + 6 additional)`);
    console.log(`   • 22 Payment components (6 original + 16 from Excel table)`);
    console.log(`   • 4 Value components`);
    console.log(`   • 8 Formulas (including tax brackets and social security calculations)`);
    console.log(`   • 6 System settings (tax tables, social security rates, allowances)`);
    console.log(`   • 3 Salary templates with components`);
    console.log(`   • 2 Attendance agreements`);
    console.log(`   • 20 Foreign workers from 4 countries`);
    console.log(`   • ${transactionCount} Salary transactions`);
    console.log(`   • ${payslipCount} Comprehensive payslips`);
    console.log(`   • ${alertCount} Compliance alerts`);
    console.log(`   • Admin user: <EMAIL> (password: *********)`);

  } catch (error) {
    console.error("❌ Seed failed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });