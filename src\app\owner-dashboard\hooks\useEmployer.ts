import { api } from "@/trpc/react";
import { useCallback, useEffect } from "react";
import { toast } from "sonner";
import type { Employer, CreateEmployerInput } from "@/schema/employer";
import type { TRPCClientErrorLike } from "@trpc/client";

interface UseEmployersResult {
  employers: Employer[] | undefined;
  totalCount: number | undefined;
  totalPages: number | undefined;
  isLoading: boolean;
  isError: boolean;
  page: number;
  setPage: (page: number) => void;
  createEmployer: (data: CreateEmployerInput) => void;
  isCreating: boolean;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for managing employers with pagination
 */
export const useEmployers = (initialPage = 1, status: "active" | "inactive" | "all" = "all"): UseEmployersResult => {
  // Setup the query with pagination
  const {
    data,
    isLoading,
    error,
    refetch,
  } = api.employer.getAll.useQuery(
    {
      page: initialPage,
      limit: 10,
      status
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // Get the tRPC utils for cache invalidation
    const utils = api.useUtils();

  // Handle errors
  useEffect(() => {
    if (error) {
      toast.error(`Failed to fetch employers: ${error.message}`);
    }
  }, [error]);

  // Create employer mutation
  const createEmployerMutation = api.employer.create.useMutation({
    onSuccess: () => {
      // Invalidate the employers list to trigger a refetch
      void utils.employer.getAll.invalidate();
      toast.success("Employer created successfully");
    },
    onError: (error) => {
      toast.error(`Failed to create employer: ${error.message}`);
    },
  });

  // Prefetch the next page for better UX
  useEffect(() => {
    if (data && data.pageCount > initialPage) {
      void utils.employer.getAll.prefetch({
        page: initialPage + 1,
        limit: 10,
        status,
      });
    }
  }, [data, initialPage, status, utils.employer.getAll]);

  // Wrapped create function for better ergonomics
  const createEmployer = useCallback(
    (input: CreateEmployerInput) => {
      createEmployerMutation.mutate(input);
    },
    [createEmployerMutation]
  );

  const refetchData = useCallback(async () => {
    await refetch();
  }, [refetch]);

  // Type casting to ensure the status type matches
  const typedEmployers = data?.employers as Employer[] | undefined;

  return {
    employers: typedEmployers,
    totalCount: data?.totalCount,
    totalPages: data?.pageCount,
    isLoading,
    isError: !!error,
    page: initialPage,
    setPage: (page: number) => {
      // This is handled externally, just for API completeness
    },
    createEmployer,
    isCreating: createEmployerMutation.isPending,
    refetch: refetchData,
  };
};

/**
 * Hook to fetch a single employer by ID
 */
export const useEmployer = (id: string | undefined) => {
  const {
    data: employer,
    isLoading,
    error,
    refetch,
  } = api.employer.getById.useQuery(
    { id: id || "" },
    {
      enabled: !!id,
      refetchOnWindowFocus: false,
    }
  );

  // Handle errors
  useEffect(() => {
    if (error) {
      toast.error(`Failed to fetch employer: ${error.message}`);
    }
  }, [error]);

    const utils = api.useUtils();

  // Update employer mutation
  const updateEmployerMutation = api.employer.update.useMutation({
    onSuccess: () => {
      // Invalidate both the list and the individual employer
      void utils.employer.getAll.invalidate();
      if (id) {
        void utils.employer.getById.invalidate({ id });
      }
      toast.success("Employer updated successfully");
    },
    onError: (error) => {
      toast.error(`Failed to update employer: ${error.message}`);
    },
  });

  // Delete employer mutation
  const deleteEmployerMutation = api.employer.delete.useMutation({
    onSuccess: () => {
      void utils.employer.getAll.invalidate();
      toast.success("Employer deleted successfully");
    },
    onError: (error) => {
      if (error.message.includes("Cannot delete employer with existing employees")) {
        toast.error("לא ניתן למחוק מעסיק עם עובדים קיימים. יש להסיר את העובדים תחילה.");
      } else {
        toast.error(`Failed to delete employer: ${error.message}`);
        console.error("Delete employer error:", error);
      }
    },
  });

  // Wrapper to ensure we always have a valid ID
  const deleteEmployer = useCallback(
    (params?: { id?: string }) => {
      // Use the ID from params if provided, otherwise use the id from props
      const deleteId = params?.id || id;
      
      if (!deleteId) {
        toast.error("לא ניתן למחוק מעסיק: מזהה חסר");
        return;
      }
      
      deleteEmployerMutation.mutate({ id: deleteId });
    },
    [deleteEmployerMutation, id]
  );

  const refetchData = useCallback(async () => {
    if (id) {
      await refetch();
    }
  }, [id, refetch]);

  // Type casting to ensure the employer type matches
  const typedEmployer = employer as Employer | undefined;

  return {
    employer: typedEmployer,
    isLoading,
    isError: !!error,
    updateEmployer: updateEmployerMutation.mutate,
    isUpdating: updateEmployerMutation.isPending,
    deleteEmployer,
    isDeleting: deleteEmployerMutation.isPending,
    refetch: refetchData,
  };
}; 