"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useCreateAgreement, useUpdateAgreement } from "../hooks";
import type { AttendanceAgreement } from "../types";
import type { FormAgreement } from "./AgreementDefinitionTab";

// Form validation schema
const agreementFormSchema = z.object({
  code: z.string().min(1, "קוד הסכם הוא שדה חובה"),
  name: z.string().min(1, "שם הסכם הוא שדה חובה"),
  description: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "DRAFT"]),
  workDaysPerWeek: z.number().min(1).max(7),
  hoursPerDay: z.number().min(1).max(24),
  monthlyHours: z.number().min(1),
  overtimeThreshold: z.number().min(0),
  nightShiftStart: z.string().optional(),
  nightShiftEnd: z.string().optional(),
  weekendDays: z.array(z.number()).optional(),
});

type AgreementFormData = z.infer<typeof agreementFormSchema>;

interface AgreementFormModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agreement: FormAgreement | null;
  onSuccess: () => void;
}

export function AgreementFormModal({
  open,
  onOpenChange,
  agreement,
  onSuccess,
}: AgreementFormModalProps) {
  const createMutation = useCreateAgreement();
  const updateMutation = useUpdateAgreement();

  const form = useForm<AgreementFormData>({
    resolver: zodResolver(agreementFormSchema),
    defaultValues: {
      code: "",
      name: "",
      description: "",
      status: "ACTIVE",
      workDaysPerWeek: 5,
      hoursPerDay: 8,
      monthlyHours: 173,
      overtimeThreshold: 8,
      nightShiftStart: "22:00",
      nightShiftEnd: "06:00",
      weekendDays: [5, 6], // Friday and Saturday
    },
  });

  // Reset form when agreement changes
  useEffect(() => {
    if (agreement && Object.keys(agreement).length > 0) {
      form.reset({
        code: agreement.code || "",
        name: agreement.name || "",
        description: agreement.description || "",
        status: agreement.status || "ACTIVE",
        workDaysPerWeek: agreement.workDaysPerWeek || 5,
        hoursPerDay: agreement.hoursPerDay || 8,
        monthlyHours: agreement.monthlyHours || 173,
        overtimeThreshold: agreement.overtimeThreshold || 8,
        nightShiftStart: agreement.nightShiftStart || "22:00",
        nightShiftEnd: agreement.nightShiftEnd || "06:00",
        weekendDays: agreement.weekendDays || [5, 6],
      });
    } else {
      form.reset({
        code: "",
        name: "",
        description: "",
        status: "ACTIVE",
        workDaysPerWeek: 5,
        hoursPerDay: 8,
        monthlyHours: 173,
        overtimeThreshold: 8,
        nightShiftStart: "22:00",
        nightShiftEnd: "06:00",
        weekendDays: [5, 6],
      });
    }
  }, [agreement, form]);

  const onSubmit = async (data: AgreementFormData) => {
    try {
      if (agreement && agreement.id) {
        await updateMutation.mutateAsync({
          id: agreement.id,
          ...data,
        });
        toast.success("הסכם עודכן בהצלחה");
      } else {
        await createMutation.mutateAsync(data);
        toast.success("הסכם נוצר בהצלחה");
      }
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      toast.error("שגיאה בשמירת הסכם", {
        description: (error as Error).message || "אירעה שגיאה",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {agreement ? "עריכת הסכם נוכחות" : "יצירת הסכם נוכחות חדש"}
          </DialogTitle>
          <DialogDescription>
            הגדר את פרטי הסכם הנוכחות. שדות המסומנים ב-* הם שדות חובה.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              {/* Code */}
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>קוד הסכם *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="לדוגמה: AGR001" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>שם הסכם *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="לדוגמה: הסכם סטנדרטי" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>תיאור</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="תיאור ההסכם..."
                      className="resize-none"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Status */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>סטטוס *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="בחר סטטוס" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ACTIVE">פעיל</SelectItem>
                      <SelectItem value="INACTIVE">לא פעיל</SelectItem>
                      <SelectItem value="DRAFT">טיוטה</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Work Parameters */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">פרמטרי עבודה</h3>
              
              <div className="grid grid-cols-2 gap-4">
                {/* Work Days Per Week */}
                <FormField
                  control={form.control}
                  name="workDaysPerWeek"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ימי עבודה בשבוע *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min={1}
                          max={7}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Hours Per Day */}
                <FormField
                  control={form.control}
                  name="hoursPerDay"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>שעות עבודה ביום *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min={1}
                          max={24}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Monthly Hours */}
                <FormField
                  control={form.control}
                  name="monthlyHours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>שעות חודשיות *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min={1}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription>
                        מספר השעות הסטנדרטי בחודש
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Overtime Threshold */}
                <FormField
                  control={form.control}
                  name="overtimeThreshold"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>סף שעות נוספות *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min={0}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription>
                        מספר שעות עבודה ביום לפני תחילת שעות נוספות
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Night Shift Times */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="nightShiftStart"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>תחילת משמרת לילה</FormLabel>
                      <FormControl>
                        <Input {...field} type="time" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nightShiftEnd"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>סיום משמרת לילה</FormLabel>
                      <FormControl>
                        <Input {...field} type="time" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                ביטול
              </Button>
              <Button
                type="submit"
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {createMutation.isPending || updateMutation.isPending
                  ? "שומר..."
                  : agreement && agreement.id
                  ? "עדכון"
                  : "יצירה"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 