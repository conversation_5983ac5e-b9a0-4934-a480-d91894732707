export enum ValueComponentType {
  VEHICLE = "VEHICLE",
  PHONE = "PHONE",
  MEAL = "MEAL",
  INTEREST = "INTEREST",
  OTHER = "OTHER",
}

export enum TaxCalculationType {
  TAX_LIABLE = "TAX_LIABLE",
  TAX_EXEMPT = "TAX_EXEMPT",
}

export enum SocialSecurityCalculationType {
  SS_LIABLE = "SS_LIABLE",
  SS_EXEMPT = "SS_EXEMPT",
}

export interface ValueComponent {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  valueComponentType: ValueComponentType;
  taxCalculation?: TaxCalculationType | null;
  socialSecurityCalculation?: SocialSecurityCalculationType | null;
  affectsPension: boolean;
  isOneTime: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface ValueComponentFormData {
  code: string;
  name: string;
  description?: string;
  valueComponentType: ValueComponentType;
  taxCalculation?: TaxCalculationType;
  socialSecurityCalculation?: SocialSecurityCalculationType;
  affectsPension: boolean;
  isOneTime: boolean;
  isActive: boolean;
}

export interface ValueComponentFilters {
  search?: string;
  valueComponentType?: ValueComponentType;
  isActive?: boolean;
  isOneTime?: boolean;
}

export interface ValueChangeLog {
  id: string;
  valueComponentId: string;
  valueComponent?: ValueComponent;
  changeType: "CREATE" | "UPDATE" | "DELETE";
  changeDetails: Record<string, any>;
  changedBy: string;
  changedAt: Date;
}

export interface ValueChangeReport {
  fromDate: Date;
  toDate: Date;
  changes: ValueChangeLog[];
  summary: {
    totalChanges: number;
    createdCount: number;
    updatedCount: number;
    deletedCount: number;
  };
}

export interface ValueUsage {
  valueComponentId: string;
  valueComponent?: ValueComponent;
  usageCount: number;
  activeEmployees: number;
  totalAmount: number;
  lastUsed?: Date;
}

export interface ValueValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export const VALUE_COMPONENT_TYPE_LABELS: Record<ValueComponentType, string> = {
  [ValueComponentType.VEHICLE]: "רכב",
  [ValueComponentType.PHONE]: "טלפון",
  [ValueComponentType.MEAL]: "ארוחות",
  [ValueComponentType.INTEREST]: "הטבות ריבית",
  [ValueComponentType.OTHER]: "אחר",
};

export const TAX_CALCULATION_LABELS: Record<TaxCalculationType, string> = {
  [TaxCalculationType.TAX_LIABLE]: "חייב מ\"ה",
  [TaxCalculationType.TAX_EXEMPT]: "פטור מ\"ה",
};

export const SOCIAL_SECURITY_LABELS: Record<SocialSecurityCalculationType, string> = {
  [SocialSecurityCalculationType.SS_LIABLE]: "חייב ב\"ל",
  [SocialSecurityCalculationType.SS_EXEMPT]: "פטור ב\"ל",
};
