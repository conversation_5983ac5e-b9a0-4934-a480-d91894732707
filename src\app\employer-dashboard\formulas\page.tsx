"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectTrigger,
	SelectValue,
	SelectContent,
	SelectItem,
} from "@/components/ui/select";
import { FormulaTable, FormulaFormModal } from "./components";
import { useFormulas } from "./hooks";
import type { FormulaType, FormulaStatus } from "./hooks";

export default function FormulasPage() {
	const [search, setSearch] = useState("");
	const [typeFilter, setTypeFilter] = useState<FormulaType | undefined>();
	const [statusFilter, setStatusFilter] = useState<FormulaStatus | undefined>();
	const [isFormOpen, setIsFormOpen] = useState(false);
	const [editing, setEditing] = useState<any | null>(null);

	const { data: formulas = [], isLoading } = useFormulas({
		search,
		type: typeFilter,
		status: statusFilter,
	});

	const formulasForTable = formulas.map(formula => ({
		...formula,
		startDate: formula.startDate.toISOString(),
		endDate: formula.endDate ? formula.endDate.toISOString() : null,
	}));

	return (
		<div className="p-6 space-y-6">
			<div className="flex justify-between items-center">
				<h1 className="text-2xl font-bold">נוסחאות</h1>
				<Button onClick={() => setIsFormOpen(true)}>הוספת נוסחה</Button>
			</div>
			<div className="flex flex-wrap gap-3">
				<Input
					placeholder="חיפוש"
					value={search}
					onChange={(e) => setSearch(e.target.value)}
					className="w-48"
				/>
				<Select 
					value={typeFilter} 
					onValueChange={(value: string) => setTypeFilter(value as FormulaType)}
				>
					<SelectTrigger className="w-40">
						<SelectValue placeholder="סוג" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="TAX">חישוב מס</SelectItem>
						<SelectItem value="DEDUCTION">חישוב ניכוי</SelectItem>
						<SelectItem value="PENSION">חישוב פנסיה</SelectItem>
						<SelectItem value="BENEFIT">חישוב זכאות</SelectItem>
						<SelectItem value="OTHER">אחר</SelectItem>
					</SelectContent>
				</Select>
				<Select 
					value={statusFilter} 
					onValueChange={(value: string) => setStatusFilter(value as FormulaStatus)}
				>
					<SelectTrigger className="w-40">
						<SelectValue placeholder="סטטוס" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="ACTIVE">פעיל</SelectItem>
						<SelectItem value="INACTIVE">לא פעיל</SelectItem>
						<SelectItem value="DRAFT">טיוטה</SelectItem>
					</SelectContent>
				</Select>
			</div>
			<FormulaTable
				formulas={formulasForTable}
				isLoading={isLoading}
				onEdit={(f) => {
					setEditing(f);
					setIsFormOpen(true);
				}}
				onDelete={() => {}}
			/>
			<FormulaFormModal
				open={isFormOpen}
				onClose={() => {
					setIsFormOpen(false);
					setEditing(null);
				}}
				initialData={editing || undefined}
			/>
		</div>
	);
}
