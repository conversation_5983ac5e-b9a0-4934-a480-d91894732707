"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Edit, Copy, Trash } from "lucide-react";
import type { SalaryTemplate } from "../types";
import { useDeleteSalaryTemplate, useDuplicateSalaryTemplate } from "../hooks";

interface Props {
  templates: SalaryTemplate[];
  isLoading: boolean;
  onEdit: (t: SalaryTemplate) => void;
}

export default function SalaryTemplatesTable({ templates, isLoading, onEdit }: Props) {
  const del = useDeleteSalaryTemplate();
  const dup = useDuplicateSalaryTemplate();

  const handleDelete = async (t: SalaryTemplate) => {
    if (confirm(`למחוק את התבנית ${t.name}?`)) {
      await del.mutateAsync({ id: t.id });
    }
  };

  const handleDuplicate = async (t: SalaryTemplate) => {
    await dup.mutateAsync({ id: t.id });
  };

  if (isLoading) {
    return <Skeleton className="h-48 w-full" />;
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>שם</TableHead>
          <TableHead>תיאור</TableHead>
          <TableHead />
        </TableRow>
      </TableHeader>
      <TableBody>
        {templates.map(t => (
          <TableRow key={t.id} data-testid="template-row">
            <TableCell>{t.name}</TableCell>
            <TableCell>{t.description}</TableCell>
            <TableCell className="flex justify-end gap-2">
              <Button variant="ghost" size="icon" onClick={() => onEdit(t)} aria-label="edit">
                <Edit className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={() => handleDuplicate(t)} aria-label="duplicate">
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={() => handleDelete(t)} aria-label="delete">
                <Trash className="h-4 w-4" />
              </Button>
            </TableCell>
          </TableRow>
        ))}
        {templates.length === 0 && (
          <TableRow>
            <TableCell colSpan={3} className="text-center">
              אין נתונים
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
