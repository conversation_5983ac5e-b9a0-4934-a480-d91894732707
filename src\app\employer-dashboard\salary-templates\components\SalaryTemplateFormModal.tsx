"use client";

import { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type { SalaryTemplate } from "../types";
import { useCreateSalaryTemplate, useUpdateSalaryTemplate } from "../hooks";

const componentSchema = z.object({
  componentType: z.enum(["PAYMENT", "VALUE", "DEDUCTION"]),
  componentCode: z.string().min(1),
  componentName: z.string().min(1),
});

const formSchema = z.object({
  employerId: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  components: z.array(componentSchema),
});

type FormSchema = z.infer<typeof formSchema>;

export default function SalaryTemplateFormModal({
  open,
  onClose,
  template,
}: {
  open: boolean;
  onClose: () => void;
  template: SalaryTemplate | null;
}) {
  const isEdit = !!template;
  const createMutation = useCreateSalaryTemplate();
  const updateMutation = useUpdateSalaryTemplate();

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employerId: template?.employerId || "",
      name: template?.name || "",
      description: template?.description || "",
      components: template?.SalaryTemplateComponent?.map(c => ({
        componentType: c.componentType as "PAYMENT" | "VALUE" | "DEDUCTION",
        componentCode: c.componentCode || "",
        componentName: c.componentName,
      })) || [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    name: "components",
    control: form.control,
  });

  useEffect(() => {
    if (template) {
      form.reset({
        employerId: template.employerId,
        name: template.name,
        description: template.description || "",
        components: template.SalaryTemplateComponent.map(c => ({
          componentType: c.componentType as "PAYMENT" | "VALUE" | "DEDUCTION",
          componentCode: c.componentCode || "",
          componentName: c.componentName,
        })),
      });
    } else {
      form.reset({ employerId: "", name: "", description: "", components: [] });
    }
  }, [template, form]);

  const onSubmit = form.handleSubmit(async (data: FormSchema) => {
    if (isEdit && template) {
      await updateMutation.mutateAsync({ id: template.id, ...data });
    } else {
      await createMutation.mutateAsync(data);
    }
    onClose();
  });

  const loading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEdit ? "עריכת תבנית" : "הוספת תבנית"}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="employerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>מעסיק</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>שם</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>תיאור</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="space-y-2">
              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name={`components.${index}.componentType` as const}
                    render={({ field }) => (
                      <FormItem className="w-32">
                        <FormLabel>סוג</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`components.${index}.componentCode` as const}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>קוד</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`components.${index}.componentName` as const}
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>שם</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="button" variant="ghost" onClick={() => remove(index)}>
                    מחק
                  </Button>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={() => append({ componentType: "PAYMENT", componentCode: "", componentName: "" })}>
                הוסף רכיב
              </Button>
            </div>
            <Button type="submit" disabled={loading} className="w-full">
              {isEdit ? "שמירה" : "יצירה"}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
