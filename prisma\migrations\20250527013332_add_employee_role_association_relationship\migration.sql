-- CreateEnum
CREATE TYPE "AgreementStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'DRAFT');

-- CreateEnum
CREATE TYPE "DayType" AS ENUM ('WEEKDAY', 'WEEKEND', 'HOLIDAY');

-- C<PERSON><PERSON>num
CREATE TYPE "MovementCategory" AS ENUM ('CHECK_IN', 'CHECK_OUT', 'BREAK_START', 'BREAK_END', 'OTHER');

-- CreateEnum
CREATE TYPE "MovementSource" AS ENUM ('MANUAL', 'MOBILE_APP', 'WEB_APP', 'BIOMETRIC', 'CARD_READER', 'API');

-- Create<PERSON>num
CREATE TYPE "DeductionType" AS ENUM ('INCOME_TAX', 'NATIONAL_INSURANCE', 'HEALTH_INSURANCE', 'PENSION', 'EDUCATION_FUND', 'LOAN_REPAYMENT', 'OTHER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "TaxCalculationType" AS ENUM ('TAX_LIABLE', 'TAX_EXEMPT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "SocialSecurityCalculationType" AS ENUM ('SS_LIABLE', 'SS_EXEMPT');

-- CreateEnum
CREATE TYPE "DeductionGroup" AS ENUM ('MANDATORY', 'VOLUNTARY', 'OTHER');

-- CreateEnum
CREATE TYPE "PaymentType" AS ENUM ('ALLOWANCE', 'EXPENSE', 'REIMBURSEMENT', 'CASH_REDEMPTION', 'OVERTIME', 'OTHER');

-- CreateEnum
CREATE TYPE "PaymentGroup" AS ENUM ('BASIC', 'SUPPLEMENT');

-- CreateEnum
CREATE TYPE "ValueComponentType" AS ENUM ('VEHICLE', 'PHONE', 'MEAL', 'INTEREST', 'OTHER');

-- CreateEnum
CREATE TYPE "FormulaType" AS ENUM ('TAX', 'DEDUCTION', 'PENSION', 'BENEFIT', 'OTHER');

-- CreateEnum
CREATE TYPE "FormulaStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'DRAFT');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "AssociationType" ADD VALUE 'COMPANY';
ALTER TYPE "AssociationType" ADD VALUE 'SITE';

-- DropForeignKey
ALTER TABLE "Association" DROP CONSTRAINT "Association_salaryAgreementId_fkey";

-- DropForeignKey
ALTER TABLE "Association" DROP CONSTRAINT "Association_salaryTemplateId_fkey";

-- AlterTable
ALTER TABLE "Association" ADD COLUMN     "deletedAt" TIMESTAMP(3),
ADD COLUMN     "entityId" UUID,
ADD COLUMN     "entityName" TEXT,
ALTER COLUMN "associationType" DROP NOT NULL;

-- AlterTable
ALTER TABLE "PayslipItem" ADD COLUMN     "deductionComponentId" UUID,
ADD COLUMN     "deletedAt" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "AttendanceAgreement" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "status" "AgreementStatus" NOT NULL DEFAULT 'ACTIVE',
    "workDaysPerWeek" INTEGER NOT NULL DEFAULT 5,
    "hoursPerDay" DOUBLE PRECISION NOT NULL DEFAULT 8,
    "monthlyHours" DOUBLE PRECISION NOT NULL DEFAULT 173,
    "overtimeThreshold" DOUBLE PRECISION NOT NULL DEFAULT 8,
    "nightShiftStart" TEXT,
    "nightShiftEnd" TEXT,
    "weekendDays" INTEGER[] DEFAULT ARRAY[5, 6]::INTEGER[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "AttendanceAgreement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmployeeAgreement" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "agreementId" UUID NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "EmployeeAgreement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Shift" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "agreementId" UUID NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "breakMinutes" INTEGER NOT NULL DEFAULT 0,
    "isNightShift" BOOLEAN NOT NULL DEFAULT false,
    "isFlexible" BOOLEAN NOT NULL DEFAULT false,
    "color" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "Shift_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OvertimeRule" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "agreementId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "fromHour" DOUBLE PRECISION NOT NULL,
    "toHour" DOUBLE PRECISION,
    "rate" DOUBLE PRECISION NOT NULL DEFAULT 1.25,
    "dayType" "DayType" NOT NULL DEFAULT 'WEEKDAY',
    "priority" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "OvertimeRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BreakRule" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "agreementId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "minWorkHours" DOUBLE PRECISION NOT NULL,
    "breakDuration" INTEGER NOT NULL,
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "isMandatory" BOOLEAN NOT NULL DEFAULT true,
    "canBeSplit" BOOLEAN NOT NULL DEFAULT false,
    "minSplitDuration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "BreakRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Location" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "address" TEXT,
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "radius" INTEGER,
    "timezone" TEXT NOT NULL DEFAULT 'Asia/Jerusalem',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MovementType" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" "MovementCategory" NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "requiresApproval" BOOLEAN NOT NULL DEFAULT false,
    "color" TEXT,
    "icon" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "MovementType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Movement" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "typeId" UUID NOT NULL,
    "locationId" UUID,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "source" "MovementSource" NOT NULL DEFAULT 'MANUAL',
    "ipAddress" TEXT,
    "deviceId" TEXT,
    "notes" TEXT,
    "isApproved" BOOLEAN NOT NULL DEFAULT true,
    "approvedBy" UUID,
    "approvedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "Movement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DeductionComponent" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "deductionType" "DeductionType" NOT NULL,
    "taxCalculation" "TaxCalculationType" NOT NULL,
    "socialSecurityCalculation" "SocialSecurityCalculationType" NOT NULL,
    "affectsPension" BOOLEAN NOT NULL DEFAULT false,
    "percentageOfSalary" DECIMAL(5,2),
    "group" "DeductionGroup",
    "isOneTime" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "DeductionComponent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Formula" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "FormulaType" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "status" "FormulaStatus" NOT NULL DEFAULT 'DRAFT',
    "formulaCode" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Formula_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ValueComponent" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "valueComponentType" "ValueComponentType" NOT NULL,
    "taxCalculation" "TaxCalculationType",
    "socialSecurityCalculation" "SocialSecurityCalculationType",
    "affectsPension" BOOLEAN NOT NULL DEFAULT false,
    "isOneTime" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "ValueComponent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Setting" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "key" TEXT NOT NULL,
    "value" JSONB NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Setting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PaymentComponent" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "paymentType" "PaymentType" NOT NULL,
    "paymentGroup" "PaymentGroup",
    "taxCalculation" "TaxCalculationType",
    "socialSecurityCalculation" "SocialSecurityCalculationType",
    "affectsPension" BOOLEAN NOT NULL DEFAULT false,
    "isOneTime" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "PaymentComponent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "AttendanceAgreement_tenantId_idx" ON "AttendanceAgreement"("tenantId");

-- CreateIndex
CREATE INDEX "AttendanceAgreement_status_idx" ON "AttendanceAgreement"("status");

-- CreateIndex
CREATE INDEX "AttendanceAgreement_deletedAt_idx" ON "AttendanceAgreement"("deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "AttendanceAgreement_tenantId_code_key" ON "AttendanceAgreement"("tenantId", "code");

-- CreateIndex
CREATE INDEX "EmployeeAgreement_tenantId_idx" ON "EmployeeAgreement"("tenantId");

-- CreateIndex
CREATE INDEX "EmployeeAgreement_employeeId_idx" ON "EmployeeAgreement"("employeeId");

-- CreateIndex
CREATE INDEX "EmployeeAgreement_agreementId_idx" ON "EmployeeAgreement"("agreementId");

-- CreateIndex
CREATE INDEX "EmployeeAgreement_startDate_endDate_idx" ON "EmployeeAgreement"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "EmployeeAgreement_deletedAt_idx" ON "EmployeeAgreement"("deletedAt");

-- CreateIndex
CREATE INDEX "Shift_tenantId_idx" ON "Shift"("tenantId");

-- CreateIndex
CREATE INDEX "Shift_agreementId_idx" ON "Shift"("agreementId");

-- CreateIndex
CREATE INDEX "Shift_deletedAt_idx" ON "Shift"("deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "Shift_agreementId_code_key" ON "Shift"("agreementId", "code");

-- CreateIndex
CREATE INDEX "OvertimeRule_tenantId_idx" ON "OvertimeRule"("tenantId");

-- CreateIndex
CREATE INDEX "OvertimeRule_agreementId_idx" ON "OvertimeRule"("agreementId");

-- CreateIndex
CREATE INDEX "OvertimeRule_dayType_idx" ON "OvertimeRule"("dayType");

-- CreateIndex
CREATE INDEX "OvertimeRule_priority_idx" ON "OvertimeRule"("priority");

-- CreateIndex
CREATE INDEX "OvertimeRule_deletedAt_idx" ON "OvertimeRule"("deletedAt");

-- CreateIndex
CREATE INDEX "BreakRule_tenantId_idx" ON "BreakRule"("tenantId");

-- CreateIndex
CREATE INDEX "BreakRule_agreementId_idx" ON "BreakRule"("agreementId");

-- CreateIndex
CREATE INDEX "BreakRule_minWorkHours_idx" ON "BreakRule"("minWorkHours");

-- CreateIndex
CREATE INDEX "BreakRule_deletedAt_idx" ON "BreakRule"("deletedAt");

-- CreateIndex
CREATE INDEX "Location_tenantId_idx" ON "Location"("tenantId");

-- CreateIndex
CREATE INDEX "Location_isActive_idx" ON "Location"("isActive");

-- CreateIndex
CREATE INDEX "Location_deletedAt_idx" ON "Location"("deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "Location_tenantId_code_key" ON "Location"("tenantId", "code");

-- CreateIndex
CREATE INDEX "MovementType_tenantId_idx" ON "MovementType"("tenantId");

-- CreateIndex
CREATE INDEX "MovementType_category_idx" ON "MovementType"("category");

-- CreateIndex
CREATE INDEX "MovementType_isDefault_idx" ON "MovementType"("isDefault");

-- CreateIndex
CREATE INDEX "MovementType_deletedAt_idx" ON "MovementType"("deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "MovementType_tenantId_code_key" ON "MovementType"("tenantId", "code");

-- CreateIndex
CREATE INDEX "Movement_tenantId_idx" ON "Movement"("tenantId");

-- CreateIndex
CREATE INDEX "Movement_employeeId_idx" ON "Movement"("employeeId");

-- CreateIndex
CREATE INDEX "Movement_typeId_idx" ON "Movement"("typeId");

-- CreateIndex
CREATE INDEX "Movement_locationId_idx" ON "Movement"("locationId");

-- CreateIndex
CREATE INDEX "Movement_timestamp_idx" ON "Movement"("timestamp");

-- CreateIndex
CREATE INDEX "Movement_source_idx" ON "Movement"("source");

-- CreateIndex
CREATE INDEX "Movement_deletedAt_idx" ON "Movement"("deletedAt");

-- CreateIndex
CREATE INDEX "DeductionComponent_tenantId_employerId_deductionType_group__idx" ON "DeductionComponent"("tenantId", "employerId", "deductionType", "group", "isActive", "deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "DeductionComponent_tenantId_code_key" ON "DeductionComponent"("tenantId", "code");

-- CreateIndex
CREATE INDEX "Formula_tenantId_idx" ON "Formula"("tenantId");

-- CreateIndex
CREATE INDEX "Formula_employerId_idx" ON "Formula"("employerId");

-- CreateIndex
CREATE INDEX "Formula_type_idx" ON "Formula"("type");

-- CreateIndex
CREATE INDEX "Formula_status_idx" ON "Formula"("status");

-- CreateIndex
CREATE INDEX "ValueComponent_tenantId_employerId_valueComponentType_isAct_idx" ON "ValueComponent"("tenantId", "employerId", "valueComponentType", "isActive", "deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "ValueComponent_tenantId_code_key" ON "ValueComponent"("tenantId", "code");

-- CreateIndex
CREATE INDEX "Setting_tenantId_idx" ON "Setting"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "Setting_tenantId_key_key" ON "Setting"("tenantId", "key");

-- CreateIndex
CREATE INDEX "PaymentComponent_tenantId_employerId_paymentType_paymentGro_idx" ON "PaymentComponent"("tenantId", "employerId", "paymentType", "paymentGroup", "isActive", "deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "PaymentComponent_tenantId_employerId_code_key" ON "PaymentComponent"("tenantId", "employerId", "code");

-- CreateIndex
CREATE INDEX "Association_entityId_idx" ON "Association"("entityId");

-- CreateIndex
CREATE INDEX "Association_deletedAt_idx" ON "Association"("deletedAt");

-- CreateIndex
CREATE INDEX "PayslipItem_deductionComponentId_idx" ON "PayslipItem"("deductionComponentId");

-- CreateIndex
CREATE INDEX "PayslipItem_deletedAt_idx" ON "PayslipItem"("deletedAt");

-- AddForeignKey
ALTER TABLE "PayslipItem" ADD CONSTRAINT "PayslipItem_deductionComponentId_fkey" FOREIGN KEY ("deductionComponentId") REFERENCES "DeductionComponent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendanceAgreement" ADD CONSTRAINT "AttendanceAgreement_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmployeeAgreement" ADD CONSTRAINT "EmployeeAgreement_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmployeeAgreement" ADD CONSTRAINT "EmployeeAgreement_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmployeeAgreement" ADD CONSTRAINT "EmployeeAgreement_agreementId_fkey" FOREIGN KEY ("agreementId") REFERENCES "AttendanceAgreement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Shift" ADD CONSTRAINT "Shift_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Shift" ADD CONSTRAINT "Shift_agreementId_fkey" FOREIGN KEY ("agreementId") REFERENCES "AttendanceAgreement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OvertimeRule" ADD CONSTRAINT "OvertimeRule_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OvertimeRule" ADD CONSTRAINT "OvertimeRule_agreementId_fkey" FOREIGN KEY ("agreementId") REFERENCES "AttendanceAgreement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BreakRule" ADD CONSTRAINT "BreakRule_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BreakRule" ADD CONSTRAINT "BreakRule_agreementId_fkey" FOREIGN KEY ("agreementId") REFERENCES "AttendanceAgreement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Location" ADD CONSTRAINT "Location_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MovementType" ADD CONSTRAINT "MovementType_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Movement" ADD CONSTRAINT "Movement_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Movement" ADD CONSTRAINT "Movement_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Movement" ADD CONSTRAINT "Movement_typeId_fkey" FOREIGN KEY ("typeId") REFERENCES "MovementType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Movement" ADD CONSTRAINT "Movement_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DeductionComponent" ADD CONSTRAINT "DeductionComponent_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DeductionComponent" ADD CONSTRAINT "DeductionComponent_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Formula" ADD CONSTRAINT "Formula_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Formula" ADD CONSTRAINT "Formula_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ValueComponent" ADD CONSTRAINT "ValueComponent_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ValueComponent" ADD CONSTRAINT "ValueComponent_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Setting" ADD CONSTRAINT "Setting_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentComponent" ADD CONSTRAINT "PaymentComponent_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentComponent" ADD CONSTRAINT "PaymentComponent_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;
