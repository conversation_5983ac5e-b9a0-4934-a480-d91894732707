import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import type { Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

// Input validation schemas
const deductionTypeEnum = z.enum([
  "INCOME_TAX",
  "NATIONAL_INSURANCE",
  "HEALTH_INSURANCE",
  "PENSION",
  "EDUCATION_FUND",
  "LOAN_REPAYMENT",
  "OTHER",
]);

const taxCalculationEnum = z.enum(["TAX_LIABLE", "TAX_EXEMPT"]);
const socialSecurityEnum = z.enum(["SS_LIABLE", "SS_EXEMPT"]);
const deductionGroupEnum = z.enum(["MANDATORY", "VOLUNTARY", "OTHER"]);

const createDeductionComponentSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  deductionType: deductionTypeEnum,
  taxCalculation: taxCalculationEnum,
  socialSecurityCalculation: socialSecurityEnum,
  affectsPension: z.boolean().default(false),
  percentageOfSalary: z.number().min(0).max(100).optional(),
  rewardCode: z.string().optional(),
  displayOrder: z.number().default(0),
  group: deductionGroupEnum.optional(),
  isOneTime: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

const updateDeductionComponentSchema = createDeductionComponentSchema.extend({
  id: z.string(),
});

const filtersSchema = z.object({
  search: z.string().optional(),
  deductionType: deductionTypeEnum.optional(),
  group: deductionGroupEnum.optional(),
  isActive: z.boolean().optional(),
  isOneTime: z.boolean().optional(),
}).optional();

export const deductionComponentRouter = createTRPCRouter({
  // Get all deduction components
  getAll: publicProcedure
    .input(filtersSchema)
    .query(async ({ ctx, input }) => {
      const where: Prisma.DeductionComponentWhereInput = { deletedAt: null };
      
      if (input?.search) {
        where.OR = [
          { name: { contains: input.search, mode: "insensitive" } },
          { code: { contains: input.search, mode: "insensitive" } },
          { description: { contains: input.search, mode: "insensitive" } },
        ];
      }

      if (input?.deductionType) {
        where.deductionType = input.deductionType;
      }

      if (input?.group) {
        where.group = input.group;
      }

      if (input?.isActive !== undefined) {
        where.isActive = input.isActive;
      }

      if (input?.isOneTime !== undefined) {
        where.isOneTime = input.isOneTime;
      }

      return ctx.db.deductionComponent.findMany({
        where,
        orderBy: [
          { name: "asc" },
        ],
      });
    }),

  // Get deduction component by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const component = await ctx.db.deductionComponent.findUnique({
        where: { id: input.id },
      });

      if (!component || component.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ניכוי לא נמצא",
        });
      }

      return component;
    }),
  // Create new deduction component
  create: protectedProcedure
    .input(createDeductionComponentSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if code already exists
      const existing = await ctx.db.deductionComponent.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קוד רכיב ניכוי כבר קיים במערכת",
        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      return ctx.db.deductionComponent.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update deduction component
  update: publicProcedure
    .input(updateDeductionComponentSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // Check if component exists
      const existing = await ctx.db.deductionComponent.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ניכוי לא נמצא",
        });
      }

      // Check if new code conflicts with another component
      if (data.code !== existing.code) {
        const codeExists = await ctx.db.deductionComponent.findFirst({
          where: {
            code: data.code,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (codeExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "קוד רכיב ניכוי כבר קיים במערכת",
          });
        }
      }

      return ctx.db.deductionComponent.update({
        where: { id },
        data,
      });
    }),

  // Delete deduction component (soft delete)
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if component exists
      const existing = await ctx.db.deductionComponent.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ניכוי לא נמצא",
        });
      }

      // Check if component is in use
      const inUse = await ctx.db.payslipItem.findFirst({
        where: {
          deductionComponentId: input.id,
          deletedAt: null,
        },
      });

      if (inUse) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "לא ניתן למחוק רכיב ניכוי שנמצא בשימוש בתלושי שכר",
        });
      }

      return ctx.db.deductionComponent.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
  // Duplicate deduction component
  duplicate: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const original = await ctx.db.deductionComponent.findUnique({
        where: { id: input.id },
      });

      if (!original || original.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ניכוי לא נמצא",
        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      // Generate new code
      let newCode = `${original.code}_COPY`;
      let counter = 1;

      while (true) {
        const exists = await ctx.db.deductionComponent.findFirst({
          where: {
            code: newCode,
            deletedAt: null,
          },
        });

        if (!exists) break;

        counter++;
        newCode = `${original.code}_COPY${counter}`;
      }

      return ctx.db.deductionComponent.create({
        data: {
          ...original,
          id: undefined,
          code: newCode,
          name: `${original.name} (עותק)`,
          createdAt: undefined,
          updatedAt: undefined,
          tenantId,
        },
      });
    }),

  // Validate deduction component
  validate: publicProcedure
    .input(createDeductionComponentSchema)
    .query(async ({ ctx, input }) => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check for duplicate code
      const existingCode = await ctx.db.deductionComponent.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existingCode) {
        errors.push("קוד רכיב ניכוי כבר קיים במערכת");
      }

      // Check for similar names
      const similarName = await ctx.db.deductionComponent.findFirst({
        where: {
          name: {
            equals: input.name,
            mode: "insensitive",
          },
          deletedAt: null,
        },
      });

      if (similarName) {
        warnings.push("קיים רכיב ניכוי עם שם דומה");
      }

      // Validate percentage
      if (input.percentageOfSalary && input.percentageOfSalary > 50) {
        warnings.push("אחוז ניכוי גבוה מ-50% - יש לוודא שזה נכון");
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    }),

  // Get usage statistics
  getUsageStats: publicProcedure
    .query(async ({ ctx }) => {
      const components = await ctx.db.deductionComponent.findMany({
        where: { deletedAt: null },
        include: {
          _count: {
            select: {
              payslipItems: {
                where: { deletedAt: null },
              },
            },
          },
        },
      });

      return components.map(component => ({
        deductionComponentId: component.id,
        deductionComponent: component,
        usageCount: component._count.payslipItems,
        activeEmployees: 0, // TODO(ai, 2024-06-15): Calculate from payslip items
        totalAmount: 0, // TODO(ai, 2024-06-15): Calculate from payslip items
        lastUsed: null, // TODO(ai, 2024-06-15): Get from payslip items
      }));
    }),

  // Get change report
  getChangeReport: publicProcedure
    .input(z.object({
      fromDate: z.date(),
      toDate: z.date(),
    }))
    .query(async ({ ctx, input }) => {
      // TODO(ai, 2024-06-15): Implement audit log tracking
      return {
        fromDate: input.fromDate,
        toDate: input.toDate,
        changes: [],
        summary: {
          totalChanges: 0,
          createdCount: 0,
          updatedCount: 0,
          deletedCount: 0,
        },
      };
    }),

  // Bulk update
  bulkUpdate: publicProcedure
    .input(z.object({
      ids: z.array(z.string()),
      updates: z.object({
        isActive: z.boolean().optional(),
        group: deductionGroupEnum.optional(),
        taxCalculation: taxCalculationEnum.optional(),
        socialSecurityCalculation: socialSecurityEnum.optional(),
      }),
    }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.db.deductionComponent.updateMany({
        where: {
          id: { in: input.ids },
          deletedAt: null,
        },
        data: input.updates,
      });

      return {
        updated: result.count,
      };
    }),

  // Export deduction components
  export: publicProcedure
    .mutation(async ({ ctx }) => {
      const components = await ctx.db.deductionComponent.findMany({
        where: { deletedAt: null },
        orderBy: [
          { name: "asc" },
        ],
      });

      return components;
    }),
  // Import deduction components
  import: protectedProcedure
    .input(z.array(createDeductionComponentSchema))
    .mutation(async ({ ctx, input }) => {
      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      let imported = 0;

      for (const component of input) {
        // Check if code already exists
        const existing = await ctx.db.deductionComponent.findFirst({
          where: {
            code: component.code,
            deletedAt: null,
          },
        });

        if (!existing) {
          await ctx.db.deductionComponent.create({
            data: {
              ...component,
              tenantId,
            },
          });
          imported++;
        }
      }

      return {
        imported,
        total: input.length,
      };
    }),
});