import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { paymentComponentRouter } = await import('../payment-component');
const createCaller = createCallerFactory(paymentComponentRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('paymentComponentRouter.create', () => {
  it('rejects duplicate code', async () => {
    const dbMock = {
      paymentComponent: {
        findFirst: vi.fn().mockResolvedValue({ id: 'p1' }),
      },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock });
    await expect(
      caller.create({ code: 'C1', name: 'p', paymentType: 'OTHER', affectsPension: false, isOneTime: false, isActive: true })
    ).rejects.toBeInstanceOf(Error);
  });

  it('resolves tenant from db', async () => {
    const dbMock = {
      paymentComponent: {
        findFirst: vi.fn().mockResolvedValue(null),
        create: vi.fn().mockResolvedValue({ id: 'p1' }),
      },
      user: {
        findUnique: vi.fn().mockResolvedValue({ tenantId: 't1' }),
      },
    } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1' } },
    });
    await caller.create({
      code: 'C1',
      name: 'p',
      paymentType: 'OTHER',
      affectsPension: false,
      isOneTime: false,
      isActive: true,
    });
    expect(dbMock.user.findUnique).toHaveBeenCalledWith({
      where: { id: 'u1' },
      select: { tenantId: true },
    });
    expect(dbMock.paymentComponent.create).toHaveBeenCalledWith({
      data: {
        code: 'C1',
        name: 'p',
        paymentType: 'OTHER',
        affectsPension: false,
        isOneTime: false,
        isActive: true,
        tenantId: 't1',
      },
    });
  });
});
