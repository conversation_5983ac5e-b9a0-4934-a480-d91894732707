import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/server/api/trpc";
import { LeaveType, type Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";

// Define input schema for creating a leave record
const createLeaveRecordSchema = z.object({
  employeeId: z.string().uuid(),
  leaveType: z.nativeEnum(LeaveType),
  startDate: z.date(),
  endDate: z.date(),
  taken: z.number().positive(),
  notes: z.string().optional(),
  year: z.number().int().min(2000).max(2100),
  month: z.number().int().min(1).max(12),
});

// Schema for getting leave balances
const getBalancesSchema = z.object({
  employeeId: z.string().uuid(),
});

// Schema for filtering leave records
const filterLeaveRecordsSchema = z.object({
  employeeId: z.string().uuid().optional(),
  leaveType: z.nativeEnum(LeaveType).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  year: z.number().int().optional(),
  month: z.number().int().optional(),
});

// Helper function to calculate the entitlement for a leave type based on employee and agreements
const calculateEntitlement = async (
  employeeId: string,
  leaveType: LeaveType,
  ctx: { db: any }
): Promise<number> => {
  const { db } = ctx;
  
  // Get employee details
  const employee = await db.employee.findUnique({
    where: { id: employeeId },
    select: {
      startDate: true,
      isForeign: true,
      sector: true,
    },
  });
  
  if (!employee) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Employee not found",
    });
  }
  
  // Get association to salary agreement for entitlement rules
  const association = await db.association.findFirst({
    where: {
      employeeId,
      associationType: "SALARY_AGREEMENT",
      // Check if the association is active at current time
      startDate: { lte: new Date() },
      OR: [
        { endDate: null },
        { endDate: { gte: new Date() } },
      ],
    },
    include: {
      salaryAgreement: true,
    },
  });
  
  // Calculate years of service
  const startDate = new Date(employee.startDate);
  const now = new Date();
  const yearsOfService = now.getFullYear() - startDate.getFullYear() +
    (now.getMonth() >= startDate.getMonth() && now.getDate() >= startDate.getDate() ? 0 : -1);
  
  // Default entitlements if no specific agreement is found
  let entitlement = 0;
  
  switch (leaveType) {
    case "VACATION":
      // Basic vacation entitlement rules
      // Foreign construction workers often have different entitlements
      if (employee.isForeign && employee.sector === "CONSTRUCTION") {
        entitlement = 14; // Standard 14 days for foreign construction workers
      } else {
        // Standard Israeli labor law entitlements based on years of service
        if (yearsOfService < 1) {
          entitlement = 12;
        } else if (yearsOfService < 5) {
          entitlement = 14;
        } else if (yearsOfService < 10) {
          entitlement = 18;
        } else if (yearsOfService < 15) {
          entitlement = 21;
        } else {
          entitlement = 24;
        }
      }
      break;
      
    case "SICK":
      // Standard sick leave accumulation is 1.5 days per month, up to 90 days
      entitlement = Math.min(yearsOfService * 18, 90);
      break;
      
    case "UNPAID":
      // Unpaid leave has no entitlement limits
      entitlement = 365;
      break;
      
    case "MATERNITY":
      // Maternity leave according to Israeli law
      entitlement = 26 * 7; // 26 weeks
      break;
      
    case "MILITARY":
      // Military reserve duty allowance
      entitlement = 45;
      break;
      
    case "INTER_VISA":
      // Between visas period for foreign workers
      entitlement = employee.isForeign ? 30 : 0;
      break;
      
    default:
      entitlement = 0;
      break;
  }
  
  // Override with specific agreement terms if available
  if (association?.salaryAgreement?.terms) {
    const terms = association.salaryAgreement.terms as Prisma.JsonObject;
    
    // Check if the agreement has specific entitlement for this leave type
    if (terms.leaveEntitlements) {
      const leaveEntitlements = terms.leaveEntitlements as Prisma.JsonObject;
      
      // Get entitlement for specific leave type
      if (leaveEntitlements[leaveType]) {
        const typeEntitlement = leaveEntitlements[leaveType] as Prisma.JsonObject;
        
        // Check for service-based entitlement
        if (typeEntitlement.byService) {
          const serviceEntitlements = typeEntitlement.byService as Prisma.JsonArray;
          
          // Find appropriate entitlement based on years of service
          for (const entry of serviceEntitlements) {
            const serviceEntry = entry as Prisma.JsonObject;
            const minYears = Number(serviceEntry.minYears || 0);
            const maxYears = Number(serviceEntry.maxYears || 999);
            
            if (yearsOfService >= minYears && yearsOfService <= maxYears) {
              entitlement = Number(serviceEntry.days || entitlement);
              break;
            }
          }
        } else if (typeEntitlement.days) {
          // Fixed entitlement
          entitlement = Number(typeEntitlement.days);
        }
      }
    }
  }
  
  return entitlement;
};

export const leaveRecordRouter = createTRPCRouter({
  // Create a new leave record
  create: protectedProcedure
    .input(createLeaveRecordSchema)
    .mutation(async ({ ctx, input }) => {
      const { employeeId, leaveType, startDate, endDate, taken, notes, year, month } = input;
      
      // Get the tenant ID from the authenticated user
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      
      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User must be associated with a tenant",
        });
      }
      
      // Calculate entitlement
      const entitlement = await calculateEntitlement(employeeId, leaveType, ctx);
      
      // Calculate how much leave has already been taken
      const previousLeave = await ctx.db.leaveRecord.aggregate({
        where: {
          tenantId: user.tenantId,
          employeeId,
          leaveType,
          year,
        },
        _sum: {
          taken: true,
        },
      });
      
      const previouslyTaken = previousLeave._sum.taken ? Number(previousLeave._sum.taken) : 0;
      const newTotalTaken = previouslyTaken + taken;
      
      // Check if there's enough balance for vacation leave
      if (leaveType === "VACATION" && newTotalTaken > entitlement) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `יתרת ימי החופשה של העובד (${entitlement} ימים) נמוכה מהכמות המבוקשת (${newTotalTaken} ימים כולל ניצול קודם)`,
        });
      }
      
      // Create the leave record
      const leaveRecord = await ctx.db.leaveRecord.create({
        data: {
          tenantId: user.tenantId,
          employeeId,
          leaveType,
          startDate,
          endDate,
          entitlement,
          taken,
          previousBalance: entitlement - previouslyTaken,
          notes,
          year,
          month,
        },
      });
      
      return leaveRecord;
    }),
  
  // Get leave balances for an employee
  getBalances: protectedProcedure
    .input(getBalancesSchema)
    .query(async ({ ctx, input }) => {
      const { employeeId } = input;
      
      // Get the tenant ID from the authenticated user
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      
      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User must be associated with a tenant",
        });
      }
      
      // Calculate current year
      const currentYear = new Date().getFullYear();
      
      // Get all leave types
      const leaveTypes = Object.values(LeaveType);
      
      // Calculate balances for each leave type
      const balances = await Promise.all(
        leaveTypes.map(async (leaveType) => {
          // Calculate entitlement
          const entitlement = await calculateEntitlement(employeeId, leaveType, ctx);
          
          // Get sum of taken days for current year
          const takenDays = await ctx.db.leaveRecord.aggregate({
            where: {
              tenantId: user.tenantId,
              employeeId,
              leaveType,
              year: currentYear,
            },
            _sum: {
              taken: true,
            },
          });
          
          const taken = takenDays._sum.taken ? Number(takenDays._sum.taken) : 0;
          
          return {
            leaveType,
            entitlement,
            taken,
            balance: entitlement - taken,
          };
        })
      );
      
      return balances;
    }),
  
  // Get leave records with filters
  getAll: protectedProcedure
    .input(filterLeaveRecordsSchema)
    .query(async ({ ctx, input }) => {
      const { employeeId, leaveType, startDate, endDate, year, month } = input;
      
      // Get the tenant ID from the authenticated user
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      
      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User must be associated with a tenant",
        });
      }
      
      // Build filter
      const filter: Prisma.LeaveRecordWhereInput = {
        tenantId: user.tenantId,
      };
      
      if (employeeId) filter.employeeId = employeeId;
      if (leaveType) filter.leaveType = leaveType;
      if (year) filter.year = year;
      if (month) filter.month = month;
      
      // Date range filters
      if (startDate || endDate) {
        filter.OR = [];
        
        if (startDate && endDate) {
          // Records that overlap with the date range
          filter.OR.push({
            startDate: { lte: endDate },
            endDate: { gte: startDate },
          });
        } else if (startDate) {
          // Records that start after or overlap with startDate
          filter.OR.push({
            startDate: { gte: startDate },
          });
          filter.OR.push({
            endDate: { gte: startDate },
          });
        } else if (endDate) {
          // Records that end before or overlap with endDate
          filter.OR.push({
            endDate: { lte: endDate },
          });
          filter.OR.push({
            startDate: { lte: endDate },
          });
        }
      }
      
      // Get leave records
      const leaveRecords = await ctx.db.leaveRecord.findMany({
        where: filter,
        include: {
          employee: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: [
          { year: "desc" },
          { month: "desc" },
          { startDate: "desc" },
        ],
      });
      
      return leaveRecords;
    }),
}); 