import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { calculatePayslip, recalculatePayslip } from "@/server/services/payslip";
import { z } from "zod";
import { PayslipStatus } from "@prisma/client";
import type { Prisma } from "@prisma/client";

export const payslipRouter = createTRPCRouter({
	calculate: protectedProcedure
		.input(
			z.object({
				employeeId: z.string(),
				month: z.number().min(1).max(12),
				year: z.number().min(1900),
				workHours: z.object({
					regularHours: z.number().optional(),
					overtime125: z.number().optional(),
					overtime150: z.number().optional(),
					overtime175: z.number().optional(),
					overtime200: z.number().optional(),
				}).optional(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const payslip = await calculatePayslip(ctx.db, {
				employeeId: input.employeeId,
				month: input.month,
				year: input.year,
			}, input.workHours);
			return payslip;
		}),

	getAll: protectedProcedure
		.input(
			z.object({
				employeeId: z.string().optional(),
				year: z.number().optional(),
				month: z.number().optional(),
				status: z.nativeEnum(PayslipStatus).optional(),
				limit: z.number().min(1).max(100).default(50),
				offset: z.number().default(0),
			}),
		)
		.query(async ({ ctx, input }) => {
			// Get user with tenant
			const user = await ctx.db.user.findUnique({
				where: { id: ctx.session.user.id },
				select: { tenantId: true },
			});

			if (!user?.tenantId) {
				throw new Error("משתמש לא שייך לחברה");
			}

                        const where: Prisma.PayslipWhereInput = {
                                tenantId: user.tenantId,
                        };

			if (input.employeeId) where.employeeId = input.employeeId;
			if (input.year) where.year = input.year;
			if (input.month) where.month = input.month;
			if (input.status) where.status = input.status;

			const [payslips, total] = await Promise.all([
				ctx.db.payslip.findMany({
					where,
					include: {
						employee: {
							select: {
								id: true,
								firstName: true,
								lastName: true,
								nationalId: true,
							},
						},
						items: true,
					},
					orderBy: [
						{ year: 'desc' },
						{ month: 'desc' },
						{ employee: { lastName: 'asc' } },
					],
					take: input.limit,
					skip: input.offset,
				}),
				ctx.db.payslip.count({ where }),
			]);

			return {
				payslips,
				total,
				hasMore: input.offset + input.limit < total,
			};
		}),

	getById: protectedProcedure
		.input(z.string())
		.query(async ({ ctx, input }) => {
			// Get user with tenant
			const user = await ctx.db.user.findUnique({
				where: { id: ctx.session.user.id },
				select: { tenantId: true },
			});

			if (!user?.tenantId) {
				throw new Error("משתמש לא שייך לחברה");
			}

			const payslip = await ctx.db.payslip.findUnique({
				where: { 
					id: input,
					tenantId: user.tenantId,
				},
				include: {
					employee: true,
					items: {
						orderBy: [
							{ type: 'asc' },
							{ description: 'asc' },
						],
					},
				},
			});

			if (!payslip) {
				throw new Error("תלוש שכר לא נמצא");
			}

			return payslip;
		}),

	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				grossPay: z.number().optional(),
				netPay: z.number().optional(),
				taxDeducted: z.number().optional(),
				insuranceDeducted: z.number().optional(),
				otherDeductions: z.number().optional(),
				allowances: z.number().optional(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const { id, ...data } = input;

			// Get user with tenant
			const user = await ctx.db.user.findUnique({
				where: { id: ctx.session.user.id },
				select: { tenantId: true },
			});

			if (!user?.tenantId) {
				throw new Error("משתמש לא שייך לחברה");
			}

			// Verify payslip exists and is editable
			const existing = await ctx.db.payslip.findUnique({
				where: { id, tenantId: user.tenantId },
			});

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			if (existing.status !== 'DRAFT' && existing.status !== 'CALCULATED') {
				throw new Error("לא ניתן לערוך תלוש שכר שכבר אושר או שולם");
			}

			// Convert numbers to Prisma Decimal
			const updateData: any = {};
			if (data.grossPay !== undefined) updateData.grossPay = data.grossPay;
			if (data.netPay !== undefined) updateData.netPay = data.netPay;
			if (data.taxDeducted !== undefined) updateData.taxDeducted = data.taxDeducted;
			if (data.insuranceDeducted !== undefined) updateData.insuranceDeducted = data.insuranceDeducted;
			if (data.otherDeductions !== undefined) updateData.otherDeductions = data.otherDeductions;
			if (data.allowances !== undefined) updateData.allowances = data.allowances;

			const updated = await ctx.db.payslip.update({
				where: { id },
				data: updateData,
				include: {
					employee: true,
					items: true,
				},
			});

			return updated;
		}),

	approve: protectedProcedure
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			// Get user with tenant
			const user = await ctx.db.user.findUnique({
				where: { id: ctx.session.user.id },
				select: { tenantId: true },
			});

			if (!user?.tenantId) {
				throw new Error("משתמש לא שייך לחברה");
			}

			const existing = await ctx.db.payslip.findUnique({
				where: { 
					id: input,
					tenantId: user.tenantId,
				},
			});

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			if (existing.status !== 'CALCULATED') {
				throw new Error("ניתן לאשר רק תלוש שכר במצב מחושב");
			}

			const updated = await ctx.db.payslip.update({
				where: { id: input },
				data: {
					status: 'APPROVED',
					approvedAt: new Date(),
				},
				include: {
					employee: true,
					items: true,
				},
			});

			return updated;
		}),

	markAsPaid: protectedProcedure
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			// Get user with tenant
			const user = await ctx.db.user.findUnique({
				where: { id: ctx.session.user.id },
				select: { tenantId: true },
			});

			if (!user?.tenantId) {
				throw new Error("משתמש לא שייך לחברה");
			}

			const existing = await ctx.db.payslip.findUnique({
				where: { 
					id: input,
					tenantId: user.tenantId,
				},
			});

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			if (existing.status !== 'APPROVED' && existing.status !== 'SENT') {
				throw new Error("ניתן לסמן כשולם רק תלוש שכר מאושר");
			}

			const updated = await ctx.db.payslip.update({
				where: { id: input },
				data: {
					status: 'PAID',
				},
				include: {
					employee: true,
					items: true,
				},
			});

			return updated;
		}),

	recalculate: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				workHours: z.object({
					regularHours: z.number().optional(),
					overtime125: z.number().optional(),
					overtime150: z.number().optional(),
					overtime175: z.number().optional(),
					overtime200: z.number().optional(),
				}).optional(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const payslip = await recalculatePayslip(ctx.db, input.id, input.workHours);
			return payslip;
		}),

	delete: protectedProcedure
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			// Get user with tenant
			const user = await ctx.db.user.findUnique({
				where: { id: ctx.session.user.id },
				select: { tenantId: true },
			});

			if (!user?.tenantId) {
				throw new Error("משתמש לא שייך לחברה");
			}

			const existing = await ctx.db.payslip.findUnique({
				where: { 
					id: input,
					tenantId: user.tenantId,
				},
			});

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			if (existing.status === 'PAID') {
				throw new Error("לא ניתן למחוק תלוש שכר ששולם");
			}

			await ctx.db.payslip.delete({
				where: { id: input },
			});

			return { success: true };
		}),
});
