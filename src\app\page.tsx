"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";

import { <PERSON> } from "@/components/ui/animated-hero";
import { Header } from "@/components/ui/header";
import { Screenshots } from "@/components/ui/screenshots";
import { Footer } from "@/components/ui/footer";
import { BentoDemo } from "@/components/ui/bento-grid-demo";
import { useSession } from "next-auth/react";

export default function Home() {
	const { data: session } = useSession();
	
	// Animation variants for cards
	const cardVariants = {
		hidden: { opacity: 0, y: 20 },
		visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
	};
	
	return (
		<main className="min-h-screen bg-gradient-to-br from-sky-100 via-indigo-50 to-purple-100 text-slate-800 relative overflow-hidden">
			{/* Subtle background elements */}
			<div className="absolute inset-0 bg-grid-pattern opacity-5 z-0"></div>
			<motion.div 
				className="absolute top-1/4 left-1/5 w-96 h-96 rounded-full bg-cyan-400/10 blur-3xl"
				initial={{ scale: 0.8, opacity: 0 }}
				animate={{ scale: 1, opacity: 1, transition: { duration: 1.5, ease: "easeInOut" } }}
			></motion.div>
			<motion.div 
				className="absolute bottom-1/3 right-1/4 w-80 h-80 rounded-full bg-purple-400/10 blur-3xl"
				initial={{ scale: 0.8, opacity: 0 }}
				animate={{ scale: 1, opacity: 1, transition: { duration: 1.5, delay: 0.3, ease: "easeInOut" } }}
			></motion.div>
			<motion.div 
				className="absolute top-2/3 left-1/3 w-72 h-72 rounded-full bg-green-400/10 blur-3xl"
				initial={{ scale: 0.8, opacity: 0 }}
				animate={{ scale: 1, opacity: 1, transition: { duration: 1.5, delay: 0.6, ease: "easeInOut" } }}
			></motion.div>
			
			{/* Header */}
			<Header />
			
			{/* Content container */}
			<div className="relative z-10">
				{/* Animated Hero Section - Kept intact as requested */}
				<Hero />

				{/* Feature Cards - Redesigned with new colors and animation */}
				<div className="container mx-auto px-4 py-16">
					<motion.div 
						className="max-w-7xl mx-auto"
						initial="hidden"
						whileInView="visible"
						viewport={{ once: true, amount: 0.2 }}
						transition={{ staggerChildren: 0.2 }}
					>
						<h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-slate-900 relative" id="features">
							<span className="relative inline-block">
								יתרונות המערכת
								<span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-cyan-500 rounded-full"></span>
							</span>
						</h2>
						
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
							{/* Card 1 */}
							<motion.div 
								className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:translate-y-[-5px] border border-green-200/30 group"
								variants={cardVariants}
							>
								<div className="mb-6 relative">
									<div className="absolute -inset-1 bg-gradient-to-r from-green-500 to-green-600 rounded-full opacity-75 blur-sm group-hover:opacity-100 transition duration-300"></div>
									<div className="relative bg-white rounded-full p-3 w-16 h-16 flex items-center justify-center">
										<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
										</svg>
									</div>
								</div>
								<h3 className="font-bold text-xl text-slate-800 mb-3 group-hover:text-green-700 transition-colors">ניהול מעסיקים מרובים</h3>
								<p className="text-slate-600 group-hover:text-slate-700">
									ממשק אחוד לצפייה וניהול של כל המעסיקים תחת מערכת אחת, עם אפשרויות קונפיגורציה מתקדמות והפרדה מלאה בין סביבות העבודה של כל מעסיק
								</p>
							</motion.div>

							{/* Card 2 */}
							<motion.div 
								className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:translate-y-[-5px] border border-cyan-200/30 group"
								variants={cardVariants}
							>
								<div className="mb-6 relative">
									<div className="absolute -inset-1 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full opacity-75 blur-sm group-hover:opacity-100 transition duration-300"></div>
									<div className="relative bg-white rounded-full p-3 w-16 h-16 flex items-center justify-center">
										<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-cyan-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
										</svg>
									</div>
								</div>
								<h3 className="font-bold text-xl text-slate-800 mb-3 group-hover:text-cyan-700 transition-colors">חישוב שכר אוטומטי מדויק</h3>
								<p className="text-slate-600 group-hover:text-slate-700">
									חישוב מקיף ואוטומטי של כל רכיבי השכר: שכר יסוד, תוספות, נסיעות, שעות נוספות, הבראה, מחלה וחופשה בהתאם לחוקי העבודה המעודכנים
								</p>
							</motion.div>

							{/* Card 3 */}
							<motion.div 
								className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:translate-y-[-5px] border border-purple-200/30 group"
								variants={cardVariants}
							>
								<div className="mb-6 relative">
									<div className="absolute -inset-1 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full opacity-75 blur-sm group-hover:opacity-100 transition duration-300"></div>
									<div className="relative bg-white rounded-full p-3 w-16 h-16 flex items-center justify-center">
										<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
										</svg>
									</div>
								</div>
								<h3 className="font-bold text-xl text-slate-800 mb-3 group-hover:text-purple-700 transition-colors">תאימות רגולטורית מלאה</h3>
								<p className="text-slate-600 group-hover:text-slate-700">
									עדכונים שוטפים בשינויי חקיקה, התראות חכמות לעמידה בתקנות, וטיפול אוטומטי בדרישות טפסים 101, 102, 126 ודיווחים לרשויות
								</p>
							</motion.div>

							{/* Card 4 */}
							<motion.div 
								className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:translate-y-[-5px] border border-indigo-200/30 group"
								variants={cardVariants}
							>
								<div className="mb-6 relative">
									<div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full opacity-75 blur-sm group-hover:opacity-100 transition duration-300"></div>
									<div className="relative bg-white rounded-full p-3 w-16 h-16 flex items-center justify-center">
										<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
										</svg>
									</div>
								</div>
								<h3 className="font-bold text-xl text-slate-800 mb-3 group-hover:text-indigo-700 transition-colors">דוחות וניתוח נתונים מתקדם</h3>
								<p className="text-slate-600 group-hover:text-slate-700">
									הפקת דוחות מותאמים אישית, ייצוא נתונים למגוון פורמטים, וכלי ניתוח ויזואליים חכמים לקבלת תובנות מדויקות על עלויות השכר
								</p>
							</motion.div>
						</div>
					</motion.div>

					{/* Foreign Workers Management Section - Redesigned with new colors and animation */}
					<motion.div 
						className="mt-28 max-w-7xl mx-auto" 
						id="foreign-workers"
						initial="hidden"
						whileInView="visible"
						viewport={{ once: true, amount: 0.2 }}
					>
						<h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-slate-900 relative">
							<span className="relative inline-block">
								מערכת ייעודית לניהול עובדים זרים
								<span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full"></span>
							</span>
						</h2>
						
						<motion.div 
							className="relative overflow-hidden"
							initial={{ opacity: 0, scale: 0.95 }}
							whileInView={{ opacity: 1, scale: 1 }}
							viewport={{ once: true, amount: 0.2 }}
							transition={{ duration: 0.7 }}
						>
							<div className="absolute -inset-10 bg-gradient-to-tr from-indigo-100 via-cyan-50 to-purple-100 opacity-50 blur-3xl -z-10"></div>
							<div className="bg-white/60 backdrop-blur-md rounded-3xl shadow-2xl p-10">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-10">
									<motion.div 
										className="flex flex-col gap-6"
										initial={{ opacity: 0, x: -20 }}
										whileInView={{ opacity: 1, x: 0 }}
										viewport={{ once: true }}
										transition={{ duration: 0.6, delay: 0.2 }}
									>
										<h3 className="text-2xl font-bold text-slate-800 border-r-4 border-purple-500 pr-4">פתרון מקיף לניהול עובדים זרים</h3>
										<p className="text-slate-700">
											המערכת מותאמת במיוחד לניהול ותשלום שכר לעובדים זרים בישראל, עם התמקדות בכל ההיבטים הרגולטוריים הייחודיים:
										</p>
										<ul className="space-y-3">
											{[
												"ניהול פיקדונות לעובדים זרים בהתאם לחוק",
												"התאמה לחישובי מס ייחודיים לתושבי חוץ",
												"ניהול ביטוחים וזכויות סוציאליות מותאמים",
												"מעקב אחר תוקף אשרות עבודה והתראות אוטומטיות",
												"דיווחים מדויקים בהתאם לדרישות רשות האוכלוסין וההגירה"
											].map((item, index) => (
												<motion.li 
													key={index} 
													className="flex items-start gap-3"
													initial={{ opacity: 0, x: -10 }}
													whileInView={{ opacity: 1, x: 0 }}
													viewport={{ once: true }}
													transition={{ duration: 0.5, delay: 0.1 * index + 0.4 }}
												>
													<div className="mt-1 flex-shrink-0 w-5 h-5 rounded-full bg-purple-500 flex items-center justify-center">
														<svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
															<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7"></path>
														</svg>
													</div>
													<span className="text-slate-700">{item}</span>
												</motion.li>
											))}
										</ul>
									</motion.div>
									<motion.div 
										className="flex flex-col gap-6"
										initial={{ opacity: 0, x: 20 }}
										whileInView={{ opacity: 1, x: 0 }}
										viewport={{ once: true }}
										transition={{ duration: 0.6, delay: 0.4 }}
									>
										<h3 className="text-2xl font-bold text-slate-800 border-r-4 border-cyan-500 pr-4">יתרונות ייחודיים למעסיקי עובדים זרים</h3>
										<div className="space-y-4">
											<motion.div 
												className="bg-white/80 rounded-xl p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-cyan-500"
												initial={{ opacity: 0, y: 10 }}
												whileInView={{ opacity: 1, y: 0 }}
												viewport={{ once: true }}
												transition={{ duration: 0.5, delay: 0.5 }}
											>
												<h4 className="font-semibold text-cyan-700 mb-2">חישוב פיקדון אוטומטי</h4>
												<p className="text-slate-700">חישוב מדויק של סכומי הפיקדון (13.83%-20.83%) בהתאם לענפים השונים וטיפול בתקרות המותרות</p>
											</motion.div>
											<motion.div 
												className="bg-white/80 rounded-xl p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-purple-500"
												initial={{ opacity: 0, y: 10 }}
												whileInView={{ opacity: 1, y: 0 }}
												viewport={{ once: true }}
												transition={{ duration: 0.5, delay: 0.6 }}
											>
												<h4 className="font-semibold text-purple-700 mb-2">ניכויים מותרים בלבד</h4>
												<p className="text-slate-700">המערכת מוודאת שניכויים מבוצעים בהתאם לחוק, כולל מגבלות על ניכויי דיור, ביטוח רפואי והגבלת ניכויים ל-25% מהשכר</p>
											</motion.div>
											<motion.div 
												className="bg-white/80 rounded-xl p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-green-500"
												initial={{ opacity: 0, y: 10 }}
												whileInView={{ opacity: 1, y: 0 }}
												viewport={{ once: true }}
												transition={{ duration: 0.5, delay: 0.7 }}
											>
												<h4 className="font-semibold text-green-700 mb-2">התאמה לדיווחי רשויות</h4>
												<p className="text-slate-700">יצירת דוחות ייעודיים לדיווח על העסקת עובדים זרים, טפסי 102 מותאמים וקובצי דיווח לרשות ההגירה</p>
											</motion.div>
										</div>
									</motion.div>
								</div>
							</div>
						</motion.div>
					</motion.div>

					{/* Screenshots Section */}
					<Screenshots />


					{/* Additional Features Section - Redesigned with new colors and animation */}
					<motion.div 
						className="mt-28 max-w-7xl mx-auto"
						initial="hidden"
						whileInView="visible"
						viewport={{ once: true, amount: 0.2 }}
						transition={{ staggerChildren: 0.2 }}
					>
						<h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-slate-900 relative">
							<span className="relative inline-block">
								תכונות נוספות
								<span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-green-500 rounded-full"></span>
							</span>
						</h2>
						
						<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
							{[
								{
									title: "טפסים דיגיטליים",
									text: "מילוי והגשת טפסי 101 דיגיטליים, טופס הצטרפות לעובדים חדשים וניהול מסמכים הנדרשים לרגולציה בענן",
									icon: (
										<svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
										</svg>
									),
									color: "indigo"
								},
								{
									title: "התאמה למגזרים וענפים",
									text: "התאמה מלאה לצרכים הייחודיים של מעסיקים בחקלאות, בניין, סיעוד ותעשייה עם יישום אוטומטי של תקנות ענפיות",
									icon: (
										<svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-cyan-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
										</svg>
									),
									color: "cyan"
								},
								{
									title: "אינטגרציה מלאה",
									text: "ממשקים מובנים למערכות ERP, דיווחי שעות, הנהלת חשבונות ומערכות בנקאיות להעברות תשלומים אוטומטיות",
									icon: (
										<svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
										</svg>
									),
									color: "green"
								},
							].map((feature, index) => (
								<motion.div 
									key={index} 
									className={`bg-gradient-to-br from-${feature.color}-50 to-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-${feature.color}-100 group`}
									variants={cardVariants}
								>
									<div className="flex flex-col items-center text-center gap-4">
										<div className={`p-4 rounded-2xl bg-${feature.color}-100/50 group-hover:bg-${feature.color}-100 transition-colors duration-300`}>
											{feature.icon}
										</div>
										<h3 className={`text-xl font-bold text-${feature.color}-700`}>{feature.title}</h3>
										<p className="text-slate-700">{feature.text}</p>
									</div>
								</motion.div>
							))}
						</div>
                                        </motion.div>

                                        {/* Bento Grid Section */}
                                        <div className="mt-20">
                                                <BentoDemo />
                                        </div>

                                        {/* CTA Section - Redesigned with new colors and animation */}
                                        <motion.div
                                                className="mt-32 mb-16 relative"
						id="contact"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, amount: 0.3 }}
						transition={{ duration: 0.8, ease: "easeOut" }}
					>
						<div className="absolute inset-0 bg-gradient-to-r from-green-100 via-sky-50 to-purple-100 rounded-3xl -z-10"></div>
						<div className="absolute inset-0 bg-slate-900/5 backdrop-blur-sm rounded-3xl -z-10"></div>
						<div className="max-w-4xl mx-auto text-center py-16 px-6">
							<h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">מוכנים להתחיל?</h2>
							<p className="text-lg mb-10 text-slate-700 max-w-2xl mx-auto">
								הצטרפו ליותר מ-500 מעסיקים שכבר נהנים ממערכת שכר חכמה, אוטומטית ומותאמת במיוחד לצרכים של העסק שלכם
							</p>
							<motion.div 
								className="space-y-4"
								initial={{ opacity: 0, scale: 0.9 }}
								whileInView={{ opacity: 1, scale: 1 }}
								viewport={{ once: true }}
								transition={{ duration: 0.6, delay: 0.3 }}
							>
								<Link
                                                                        href={session ? "/owner-dashboard" : "/api/auth/signin"}
									className="inline-block rounded-full bg-gradient-to-r from-green-600 to-green-500 px-12 py-4 font-bold text-white text-lg shadow-lg hover:shadow-green-500/30 transform hover:scale-105 transition-all duration-300"
								>
									{session ? "עבור למערכת" : "התחל תקופת ניסיון חינם למשך 30 יום"}
								</Link>

								{!session && (
									<motion.div 
										className="pt-3"
										initial={{ opacity: 0 }}
										whileInView={{ opacity: 1 }}
										viewport={{ once: true }}
										transition={{ duration: 0.5, delay: 0.5 }}
									>
										<Link
											href="/api/auth/signin"
											className="text-slate-600 hover:text-slate-900 transition duration-300 hover:underline underline-offset-4"
										>
											כבר יש לך חשבון? התחבר כאן
										</Link>
									</motion.div>
								)}
							</motion.div>
						</div>
					</motion.div>
				</div>
			</div>
			
			{/* Footer */}
			<Footer />
		</main>
	);
}
