import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";

// Schema for creating/updating employee roles
const employeeRoleSchema = z.object({
  name: z.string().min(1, "שם התפקיד נדרש").max(100),
  description: z.string().optional(),
  employerId: z.string().uuid(),
});

// Schema for filtering employee roles
const filterEmployeeRolesSchema = z.object({
  employerId: z.string().uuid(),
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
});

export const employeeRoleRouter = createTRPCRouter({
  // Get all employee roles with filtering and pagination
  getAll: protectedProcedure
    .input(filterEmployeeRolesSchema)
    .query(async ({ ctx, input }) => {
      const { employerId, search, page, limit } = input;
      
      // Ensure user has access to this employer's data
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      const where = {
        employerId,
        tenantId: user.tenantId,
        ...(search && {
          name: {
            contains: search,
            mode: "insensitive" as const,
          },
        }),
      };

      const [employeeRoles, total] = await Promise.all([
        ctx.db.employeeRole.findMany({
          where,
          take: limit,
          skip: (page - 1) * limit,
          orderBy: { name: "asc" },
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            updatedAt: true,
          },
        }),
        ctx.db.employeeRole.count({ where }),
      ]);

      return {
        employeeRoles,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // Get a single employee role by ID
  getById: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input: id }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      const employeeRole = await ctx.db.employeeRole.findFirst({
        where: {
          id,
          tenantId: user.tenantId,
        },
      });

      if (!employeeRole) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employee role not found",
        });
      }

      return employeeRole;
    }),

  // Create a new employee role
  create: protectedProcedure
    .input(employeeRoleSchema)
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      // Check if role with same name exists for this employer
      const existing = await ctx.db.employeeRole.findFirst({
        where: {
          name: input.name,
          employerId: input.employerId,
          tenantId: user.tenantId,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "תפקיד בשם זה כבר קיים",
        });
      }

      const employeeRole = await ctx.db.employeeRole.create({
        data: {
          ...input,
          tenantId: user.tenantId,
        },
      });

      return employeeRole;
    }),

  // Update an existing employee role
  update: protectedProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        data: employeeRoleSchema.partial(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, data } = input;

      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }

      // Check if role exists and belongs to user's tenant
      const existingRole = await ctx.db.employeeRole.findFirst({
        where: {
          id,
          tenantId: user.tenantId,
        },
      });

      if (!existingRole) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employee role not found",
        });
      }

      // If name is being updated, check for duplicates
      if (data.name && data.name !== existingRole.name) {
        const duplicate = await ctx.db.employeeRole.findFirst({
          where: {
            name: data.name,
            employerId: existingRole.employerId,
            tenantId: user.tenantId,
            id: { not: id },
          },
        });

        if (duplicate) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "תפקיד בשם זה כבר קיים",
          });
        }
      }

      const updatedRole = await ctx.db.employeeRole.update({
        where: { id },
        data,
      });

      return updatedRole;
    }),

  // Delete an employee role
  delete: protectedProcedure
    .input(z.string().uuid())
    .mutation(async ({ ctx, input: id }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      });

      if (!user?.tenantId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found or missing tenant ID",
        });
      }      // Check if role exists and belongs to user's tenant
      const employeeRole = await ctx.db.employeeRole.findFirst({
        where: {
          id,
          tenantId: user.tenantId,
        },
      });

      if (!employeeRole) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employee role not found",
        });
      }

      // Check if role has associations
      const associationCount = await ctx.db.association.count({
        where: {
          roleId: id,
          tenantId: user.tenantId,
        },
      });

      if (associationCount > 0) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "לא ניתן למחוק תפקיד שמשויך לעובדים",
        });
      }

      await ctx.db.employeeRole.delete({
        where: { id },
      });

      return { success: true };
    }),
});
