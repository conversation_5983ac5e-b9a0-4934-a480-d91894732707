// This file contains example usage of the tRPC routers for the dashboard page

import { api } from "@/trpc/react";
import React from "react";

/**
 * Hook to fetch dashboard metrics and alerts
 */
export const useDashboardData = (period: "current" | "previous" | "quarter" | "year") => {
  const metricsResult = api.dashboard.getMetrics.useQuery({ period });
  const alertsResult = api.dashboard.getSystemAlerts.useQuery();

  const refetch = () => {
    void metricsResult.refetch();
    void alertsResult.refetch();
  };

  return {
    metrics: metricsResult.data,
    alerts: alertsResult.data?.alerts,
    isLoading: metricsResult.isLoading || alertsResult.isLoading,
    error: metricsResult.error || alertsResult.error,
    refetch,
  };
};

/**
 * Hook to fetch employers with pagination
 */
export const useEmployers = (page: number, status: "active" | "inactive" | "all" = "all") => {
    const utils = api.useUtils();
  const { data, isLoading, error } = api.employer.getAll.useQuery({
    page,
    limit: 10,
    status
  });

  // Prefetch next page
  React.useEffect(() => {
    if (data && data.pageCount > page) {
      void utils.employer.getAll.prefetch({ page: page + 1, limit: 10, status });
    }
  }, [data, page, status, utils]);

  // Create employer mutation
  const createEmployerMutation = api.employer.create.useMutation({
    onSuccess: () => {
      void utils.employer.getAll.invalidate();
      console.log("Employer created successfully");
    },
  });

  return {
    employers: data?.employers,
    totalPages: data?.pageCount,
    totalCount: data?.totalCount,
    isLoading,
    error,
    createEmployer: createEmployerMutation.mutate,
    isCreating: createEmployerMutation.isPending,
  };
};

/**
 * Hook to fetch users with pagination
 */
export const useUsers = (page: number, status: "active" | "inactive" | "all" = "all", employerId?: string) => {
    const utils = api.useUtils();
  const { data, isLoading, error } = api.user.getAll.useQuery({
    page,
    limit: 10,
    status,
    employerId
  });

  // Prefetch next page
  React.useEffect(() => {
    if (data && data.pageCount > page) {
      void utils.user.getAll.prefetch({ page: page + 1, limit: 10, status, employerId });
    }
  }, [data, page, status, employerId, utils]);

  // Create user mutation
  const createUserMutation = api.user.create.useMutation({
    onSuccess: () => {
      void utils.user.getAll.invalidate();
      console.log("User created successfully");
    },
  });

  return {
    users: data?.users,
    totalPages: data?.pageCount,
    totalCount: data?.totalCount,
    isLoading,
    error,
    createUser: createUserMutation.mutate,
    isCreating: createUserMutation.isPending,
  };
};

/**
 * Hook to fetch system reports
 */
export const useReports = () => {
    const utils = api.useUtils();
  const { data, isLoading, error } = api.report.getAvailableReports.useQuery();

  // Generate report mutation
  const generateReportMutation = api.report.generateReport.useMutation({
    onSuccess: () => {
      console.log("Report generated successfully");
    },
  });

  return {
    reports: data?.reports,
    isLoading,
    error,
    generateReport: generateReportMutation.mutate,
    isGenerating: generateReportMutation.isPending,
  };
};

/**
 * Hook to fetch audit logs with pagination and filtering
 */
export const useAuditLogs = (
  page: number, 
  actionType: "all" | "create" | "update" | "delete" = "all",
  employerId?: string
) => {
    const utils = api.useUtils();
  const { data, isLoading, error } = api.auditLog.getLogs.useQuery({
    page,
    limit: 10,
    actionType,
    employerId
  });

  // Prefetch next page
  React.useEffect(() => {
    if (data && data.pageCount > page) {
      void utils.auditLog.getLogs.prefetch({ page: page + 1, limit: 10, actionType, employerId });
    }
  }, [data, page, actionType, employerId, utils]);

  return {
    logs: data?.logs,
    totalPages: data?.pageCount,
    totalCount: data?.totalCount,
    isLoading,
    error,
  };
}; 