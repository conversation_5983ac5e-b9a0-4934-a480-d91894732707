"use client";

import { api } from "@/trpc/react";
import { toast } from "sonner";
import type {
  ValueComponent,
  ValueComponentFilters,
  ValueComponentFormData,
} from "./types";

// ============================================
// Value Components Hooks
// ============================================

export function useValueComponents(filters?: ValueComponentFilters) {
  return api.valueComponent.getAll.useQuery(filters);
}

export function useValueComponent(id: string) {
  return api.valueComponent.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateValueComponent() {
    const utils = api.useUtils();

  return api.valueComponent.create.useMutation({
    onSuccess: () => {
      utils.valueComponent.getAll.invalidate();
      toast.success("רכיב ערך נוצר בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה ביצירת רכיב ערך: ${error.message}`);
    },
  });
}

export function useUpdateValueComponent() {
    const utils = api.useUtils();

  return api.valueComponent.update.useMutation({
    onSuccess: (data) => {
      utils.valueComponent.getAll.invalidate();
      utils.valueComponent.getById.invalidate({ id: data.id });
      toast.success("רכיב ערך עודכן בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בעדכון רכיב ערך: ${error.message}`);
    },
  });
}

export function useDeleteValueComponent() {
    const utils = api.useUtils();

  return api.valueComponent.delete.useMutation({
    onSuccess: () => {
      utils.valueComponent.getAll.invalidate();
      toast.success("רכיב ערך נמחק בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה במחיקת רכיב ערך: ${error.message}`);
    },
  });
}

// ============================================
// Reports and Analytics Hooks
// ============================================

export function useValueChangeReport(fromDate: Date, toDate: Date) {
  return api.valueComponent.getChangeReport.useQuery(
    { fromDate, toDate },
    { enabled: !!fromDate && !!toDate }
  );
}

export function useValueUsageStats() {
  return api.valueComponent.getUsageStats.useQuery();
}

// ============================================
// Validation Hooks
// ============================================

export function useValidateValueComponent(data: ValueComponentFormData) {
  return api.valueComponent.validate.useQuery(
    data,
    { enabled: !!data.name && !!data.code }
  );
}

// ============================================
// Bulk Operations Hooks
// ============================================

export function useBulkUpdateValueComponents() {
    const utils = api.useUtils();

  return api.valueComponent.bulkUpdate.useMutation({
    onSuccess: (data) => {
      utils.valueComponent.getAll.invalidate();
      toast.success(`${data.updated} רכיבי ערך עודכנו בהצלחה`);
    },
    onError: (error) => {
      toast.error(`שגיאה בעדכון רכיבי ערך: ${error.message}`);
    },
  });
}

export function useDuplicateValueComponent() {
    const utils = api.useUtils();

  return api.valueComponent.duplicate.useMutation({
    onSuccess: () => {
      utils.valueComponent.getAll.invalidate();
      toast.success("רכיב ערך שוכפל בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בשכפול רכיב ערך: ${error.message}`);
    },
  });
}

// ============================================
// Export/Import Hooks
// ============================================

export function useExportValueComponents() {
  return api.valueComponent.export.useMutation({
    onSuccess: (data) => {
      // Trigger download
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `value-components-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success("קובץ רכיבי הערך הורד בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בייצוא: ${error.message}`);
    },
  });
}

export function useImportValueComponents() {
    const utils = api.useUtils();

  return api.valueComponent.import.useMutation({
    onSuccess: (data) => {
      utils.valueComponent.getAll.invalidate();
      toast.success(`${data.imported} רכיבי ערך יובאו בהצלחה`);
    },
    onError: (error) => {
      toast.error(`שגיאה בייבוא: ${error.message}`);
    },
  });
}
