import { z } from "zod";
import {
  createTRPCRouter,
  publicProcedure,
  protectedProcedure,
} from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import type { Prisma } from "@prisma/client";

const componentKindEnum = z.enum(["PAYMENT", "VALUE", "DEDUCTION"]);

const componentSchema = z.object({
  componentType: componentKindEnum,
  componentCode: z.string(),
  componentName: z.string(),
  calculationType: z.string().optional(),
  fixedAmount: z.number().optional(),
  percentage: z.number().optional(),
  formula: z.string().optional(),
  isRequired: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

const createTemplateSchema = z.object({
  employerId: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  components: z.array(componentSchema).optional(),
});

const updateTemplateSchema = createTemplateSchema.extend({ id: z.string() });

export const salaryTemplateRouter = createTRPCRouter({
  list: publicProcedure
    .input(z.object({ employerId: z.string().optional() }).optional())
    .query(async ({ ctx, input }) => {
      const where: Prisma.SalaryTemplateWhereInput = {};
      if (input?.employerId) where.employerId = input.employerId;
      return ctx.db.salaryTemplate.findMany({
        where,
        include: { SalaryTemplateComponent: true },
        orderBy: { createdAt: "desc" },
      });
    }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const template = await ctx.db.salaryTemplate.findUnique({
        where: { id: input.id },
        include: { SalaryTemplateComponent: true },
      });
      if (!template) {
        throw new TRPCError({ code: "NOT_FOUND", message: "תבנית לא נמצאה" });
      }
      return template;
    }),

  create: protectedProcedure
    .input(createTemplateSchema)
    .mutation(async ({ ctx, input }) => {
      const { components = [], ...data } = input;
      const exists = await ctx.db.salaryTemplate.findFirst({
        where: { employerId: data.employerId, name: data.name },
      });
      if (exists) {
        throw new TRPCError({ code: "CONFLICT", message: "שם תבנית כבר קיים" });
      }
      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      const template = await ctx.db.salaryTemplate.create({
        data: { ...data, tenantId },
      });      for (const comp of components) {
        await validateComponent(ctx, data.employerId, comp);
        const componentData = {
          templateId: template.id,
          componentType: comp.componentType,
          componentCode: comp.componentCode,
          componentName: comp.componentName,
          calculationType: comp.calculationType ?? "FIXED",
          fixedAmount: comp.fixedAmount ?? null,
          percentage: comp.percentage ?? null,
          formula: comp.formula ?? null,
          isRequired: comp.isRequired,
          isActive: comp.isActive,
        };
        await ctx.db.salaryTemplateComponent.create({
          data: componentData,
        });
      }

      return template;
    }),

  update: protectedProcedure
    .input(updateTemplateSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, components = [], ...data } = input;
      const template = await ctx.db.salaryTemplate.findUnique({
        where: { id },
      });
      if (!template) {
        throw new TRPCError({ code: "NOT_FOUND", message: "תבנית לא נמצאה" });
      }
      if (data.name !== template.name) {
        const nameExists = await ctx.db.salaryTemplate.findFirst({
          where: {
            employerId: template.employerId,
            name: data.name,
            id: { not: id },
          },
        });
        if (nameExists) {
          throw new TRPCError({ code: "CONFLICT", message: "שם תבנית כבר קיים" });
        }
      }      await ctx.db.salaryTemplateComponent.deleteMany({ where: { templateId: id } });
      for (const comp of components) {
        await validateComponent(ctx, template.employerId, comp);
        const componentData = {
          templateId: id,
          componentType: comp.componentType,
          componentCode: comp.componentCode,
          componentName: comp.componentName,
          calculationType: comp.calculationType ?? "FIXED",
          fixedAmount: comp.fixedAmount ?? null,
          percentage: comp.percentage ?? null,
          formula: comp.formula ?? null,
          isRequired: comp.isRequired,
          isActive: comp.isActive,
        };
        await ctx.db.salaryTemplateComponent.create({
          data: componentData,
        });
      }
      return ctx.db.salaryTemplate.update({ where: { id }, data });
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.salaryTemplateComponent.deleteMany({ where: { templateId: input.id } });
      return ctx.db.salaryTemplate.delete({ where: { id: input.id } });
    }),

  duplicate: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const template = await ctx.db.salaryTemplate.findUnique({
        where: { id: input.id },
        include: { SalaryTemplateComponent: true },
      });
      if (!template) {
        throw new TRPCError({ code: "NOT_FOUND", message: "תבנית לא נמצאה" });
      }
      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      let newName = `${template.name} (עותק)`;
      let counter = 1;
      while (true) {
        const exists = await ctx.db.salaryTemplate.findFirst({
          where: { employerId: template.employerId, name: newName },
        });
        if (!exists) break;
        counter++;
        newName = `${template.name} (עותק ${counter})`;
      }
      const newTemplate = await ctx.db.salaryTemplate.create({
        data: {
          employerId: template.employerId,
          name: newName,
          description: template.description || undefined,
          tenantId,
        },
      });
      for (const comp of template.SalaryTemplateComponent) {
        await ctx.db.salaryTemplateComponent.create({
          data: { ...comp, id: undefined as any, templateId: newTemplate.id },
        });
      }
      return newTemplate;
    }),
});

async function validateComponent(ctx: any, employerId: string, comp: z.infer<typeof componentSchema>) {
  if (comp.componentType === "PAYMENT") {
    const exists = await ctx.db.paymentComponent.findFirst({
      where: { code: comp.componentCode, employerId, deletedAt: null },
    });
    if (!exists) {
      throw new TRPCError({ code: "BAD_REQUEST", message: `רכיב תשלום ${comp.componentCode} לא קיים` });
    }
  }
  if (comp.componentType === "DEDUCTION") {
    const exists = await ctx.db.deductionComponent.findFirst({
      where: { code: comp.componentCode, employerId, deletedAt: null },
    });
    if (!exists) {
      throw new TRPCError({ code: "BAD_REQUEST", message: `רכיב ניכוי ${comp.componentCode} לא קיים` });
    }
  }
  if (comp.componentType === "VALUE") {
    const exists = await ctx.db.valueComponent.findFirst({
      where: { code: comp.componentCode, employerId, deletedAt: null },
    });
    if (!exists) {
      throw new TRPCError({ code: "BAD_REQUEST", message: `רכיב שווי ${comp.componentCode} לא קיים` });
    }
  }
}
