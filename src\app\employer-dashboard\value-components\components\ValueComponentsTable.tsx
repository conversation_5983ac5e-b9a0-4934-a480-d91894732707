"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChevronUp,
  ChevronDown,
  MoreHorizontal,
  Edit,
  Copy,
  Trash,
  Check,
  X,
} from "lucide-react";
import type {
  ValueComponent,
  ValueComponentFilters,
} from "../types";
import {
  VALUE_COMPONENT_TYPE_LABELS,
  TAX_CALCULATION_LABELS,
  SOCIAL_SECURITY_LABELS,
} from "../types";
import { useDeleteValueComponent, useDuplicateValueComponent } from "../hooks";
import { cn } from "@/lib/utils";

interface ValueComponentsTableProps {
  components: ValueComponent[];
  isLoading: boolean;
  onEdit: (component: ValueComponent) => void;
  onFiltersChange: (filters: ValueComponentFilters) => void;
  filters: ValueComponentFilters;
}

type SortField = "name" | "valueComponentType";
type SortOrder = "asc" | "desc";

export default function ValueComponentsTable({
  components,
  isLoading,
  onEdit,
  onFiltersChange,
  filters,
}: ValueComponentsTableProps) {
  const [sortField, setSortField] = useState<SortField>("name");
  const [sortOrder, setSortOrder] = useState<SortOrder>("asc");
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  const deleteComponent = useDeleteValueComponent();
  const duplicateComponent = useDuplicateValueComponent();

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("asc");
    }
  };

  const sortedComponents = [...components].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue === null || aValue === undefined) return 1;
    if (bValue === null || bValue === undefined) return -1;
    
    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    }
    
    return 0;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(new Set(components.map(c => c.id)));
    } else {
      setSelectedIds(new Set());
    }
  };

  const handleSelectOne = (id: string, checked: boolean) => {
    const newSelectedIds = new Set(selectedIds);
    if (checked) {
      newSelectedIds.add(id);
    } else {
      newSelectedIds.delete(id);
    }
    setSelectedIds(newSelectedIds);
  };

  const handleDelete = async (component: ValueComponent) => {
    if (confirm(`האם אתה בטוח שברצונך למחוק את רכיב הערך "${component.name}"?`)) {
      await deleteComponent.mutateAsync({ id: component.id });
    }
  };

  const handleDuplicate = async (component: ValueComponent) => {
    await duplicateComponent.mutateAsync({ id: component.id });
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) {
      return <ChevronUp className="h-3 w-3 text-gray-300" />;
    }
    return sortOrder === "asc" 
      ? <ChevronUp className="h-3 w-3" />
      : <ChevronDown className="h-3 w-3" />;
  };

  if (isLoading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={selectedIds.size === components.length && components.length > 0}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead 
              className="cursor-pointer"
              onClick={() => handleSort("name")}
            >
              <div className="flex items-center gap-1">
                שם התשלום
                <SortIcon field="name" />
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => handleSort("valueComponentType")}
            >
              <div className="flex items-center gap-1">
                סוג ערך
                <SortIcon field="valueComponentType" />
              </div>
            </TableHead>
            <TableHead>חישוב מס</TableHead>
            <TableHead>חישוב ביטוח לאומי</TableHead>
            <TableHead className="text-center">קובע לקצבה ופיצויים</TableHead>
            <TableHead className="text-center">חד פעמי</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedComponents.map((component) => (
            <TableRow key={component.id}>
              <TableCell>
                <Checkbox
                  checked={selectedIds.has(component.id)}
                  onCheckedChange={(checked) => 
                    handleSelectOne(component.id, checked as boolean)
                  }
                />
              </TableCell>
              <TableCell className="font-medium">
                <div>
                  <div>{component.name}</div>
                  {component.description && (
                    <div className="text-sm text-gray-500">{component.description}</div>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline">
                  {VALUE_COMPONENT_TYPE_LABELS[component.valueComponentType]}
                </Badge>
              </TableCell>
              <TableCell>
                <span className={cn(
                  "text-sm",
                  component.taxCalculation === "TAX_LIABLE" 
                    ? "text-orange-600" 
                    : "text-green-600"
                )}>
                  {component.taxCalculation ? TAX_CALCULATION_LABELS[component.taxCalculation] : "-"}
                </span>
              </TableCell>
              <TableCell>
                <span className={cn(
                  "text-sm",
                  component.socialSecurityCalculation === "SS_LIABLE" 
                    ? "text-orange-600" 
                    : "text-green-600"
                )}>
                  {component.socialSecurityCalculation ? SOCIAL_SECURITY_LABELS[component.socialSecurityCalculation] : "-"}
                </span>
              </TableCell>
              <TableCell className="text-center">
                {component.affectsPension ? (
                  <Check className="h-4 w-4 text-green-600 mx-auto" />
                ) : (
                  <X className="h-4 w-4 text-gray-300 mx-auto" />
                )}
              </TableCell>
              <TableCell className="text-center">
                {component.isOneTime ? (
                  <Check className="h-4 w-4 text-green-600 mx-auto" />
                ) : (
                  <X className="h-4 w-4 text-gray-300 mx-auto" />
                )}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onEdit(component)}>
                      <Edit className="h-4 w-4 ml-2" />
                      עריכה
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDuplicate(component)}>
                      <Copy className="h-4 w-4 ml-2" />
                      שכפול
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDelete(component)}
                      className="text-red-600"
                    >
                      <Trash className="h-4 w-4 ml-2" />
                      מחיקה
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {components.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          לא נמצאו רכיבי ערך
        </div>
      )}
    </div>
  );
} 
