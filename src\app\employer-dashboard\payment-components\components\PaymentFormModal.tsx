"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import type { PaymentComponent } from "../types";
import {
  PaymentType,
  TaxCalculationType,
  SocialSecurityCalculationType,
  PAYMENT_TYPE_LABELS,
  TAX_CALCULATION_LABELS,
  SOCIAL_SECURITY_LABELS,
  PAYMENT_GROUP_LABELS,
  PaymentGroup,
} from "../types";
import {
  useCreatePaymentComponent,
  useUpdatePaymentComponent,
} from "../hooks";

const formSchema = z.object({
  code: z.string().min(1, "קוד חובה"),
  name: z.string().min(1, "שם חובה"),
  description: z.string().optional(),
  paymentType: z.nativeEnum(PaymentType),
  taxCalculation: z.nativeEnum(TaxCalculationType),
  socialSecurityCalculation: z.nativeEnum(SocialSecurityCalculationType),
  affectsPension: z.boolean().default(false),
  paymentGroup: z.nativeEnum(PaymentGroup).optional(),
  isOneTime: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

interface PaymentFormModalProps {
  open: boolean;
  onClose: () => void;
  paymentComponent?: PaymentComponent | null;
}

export default function PaymentFormModal({
  open,
  onClose,
  paymentComponent,
}: PaymentFormModalProps) {
  const isEdit = !!paymentComponent;
  const createMutation = useCreatePaymentComponent();
  const updateMutation = useUpdatePaymentComponent();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: "",
      name: "",
      description: "",
      paymentType: PaymentType.OTHER,
      taxCalculation: TaxCalculationType.TAX_LIABLE,
      socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
      affectsPension: false,
      paymentGroup: undefined,
      isOneTime: false,
      isActive: true,
    },
  });

  useEffect(() => {
    if (paymentComponent) {
      form.reset({
        code: paymentComponent.code,
        name: paymentComponent.name,
        description: paymentComponent.description || "",
        paymentType: paymentComponent.paymentType,
        taxCalculation: paymentComponent.taxCalculation,
        socialSecurityCalculation: paymentComponent.socialSecurityCalculation,
        affectsPension: paymentComponent.affectsPension,
        paymentGroup: paymentComponent.paymentGroup || undefined,
        isOneTime: paymentComponent.isOneTime,
        isActive: paymentComponent.isActive,
      });
    } else {
      form.reset();
    }
  }, [paymentComponent, form]);

  const onSubmit = async (data: any) => {
    try {
      if (isEdit) {
        await updateMutation.mutateAsync({
          id: paymentComponent.id,
          ...data,
        });
      } else {
        await createMutation.mutateAsync(data);
      }
      onClose();
    } catch (error) {
      // Error is handled by the mutation hooks
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? "עריכת רכיב תשלום" : "הוספת רכיב תשלום"}
          </DialogTitle>
          <DialogDescription>
            {isEdit
              ? "עדכן את פרטי רכיב התשלום"
              : "הזן את פרטי רכיב התשלום החדש"}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>קוד רכיב *</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isEdit} />
                    </FormControl>
                    <FormDescription>
                      קוד ייחודי לזיהוי הרכיב
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>שם רכיב *</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>תיאור</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={3} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="paymentType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>סוג תשלום *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value as string}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(PAYMENT_TYPE_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentGroup"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>קבוצה</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="בחר קבוצה" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(PAYMENT_GROUP_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="taxCalculation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>חישוב מס *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(TAX_CALCULATION_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="socialSecurityCalculation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>חישוב ביטוח לאומי *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(SOCIAL_SECURITY_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>



            <div className="space-y-4">
              <FormField
                control={form.control}
                name="affectsPension"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        קובע לקצבה ופיצויים
                      </FormLabel>
                      <FormDescription>
                        האם הרכיב משפיע על חישוב קצבה ופיצויי פיטורין
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isOneTime"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">תשלום חד פעמי</FormLabel>
                      <FormDescription>
                        האם זהו תשלום חד פעמי שלא חוזר על עצמו
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">פעיל</FormLabel>
                      <FormDescription>
                        האם הרכיב פעיל וזמין לשימוש
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={onClose}>
                ביטול
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
                {isEdit ? "עדכון" : "הוספה"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
