"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	<PERSON><PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useCreateSalaryAgreement, useUpdateSalaryAgreement } from "../hooks";
import type { SalaryAgreement } from "../types";

const formSchema = z.object({
	employerId: z.string(),
	name: z.string().min(1),
	description: z.string().optional(),
	effectiveFrom: z.string().optional(),
	effectiveTo: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface Props {
	open: boolean;
	onClose: () => void;
	agreement: SalaryAgreement | null;
}

export default function SalaryAgreementFormModal({
	open,
	onClose,
	agreement,
}: Props) {
	const createMutation = useCreateSalaryAgreement();
	const updateMutation = useUpdateSalaryAgreement();
	const isEdit = !!agreement;

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			employerId: agreement?.employerId || "",
			name: agreement?.name || "",
			description: agreement?.description || "",
			effectiveFrom: agreement?.effectiveFrom?.toISOString().slice(0, 10) || "",
			effectiveTo: agreement?.effectiveTo?.toISOString().slice(0, 10) || "",
		},
	});

	useEffect(() => {
		if (agreement) {
			form.reset({
				employerId: agreement.employerId,
				name: agreement.name,
				description: agreement.description || "",
				effectiveFrom:
					agreement.effectiveFrom?.toISOString().slice(0, 10) || "",
				effectiveTo: agreement.effectiveTo?.toISOString().slice(0, 10) || "",
			});
		} else {
			form.reset({
				employerId: "",
				name: "",
				description: "",
				effectiveFrom: "",
				effectiveTo: "",
			});
		}
	}, [agreement, form]);

	const onSubmit = async (data: FormValues) => {
		const formattedData = {
			...data,
			effectiveFrom: data.effectiveFrom ? new Date(data.effectiveFrom) : undefined,
			effectiveTo: data.effectiveTo ? new Date(data.effectiveTo) : undefined,
		};

		if (isEdit && agreement) {
			await updateMutation.mutateAsync({ id: agreement.id, ...formattedData });
		} else {
			await createMutation.mutateAsync(formattedData);
		}
		onClose();
	};

	const loading = createMutation.isPending || updateMutation.isPending;

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>{isEdit ? "עריכת הסכם" : "הוספת הסכם"}</DialogTitle>
				</DialogHeader>
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="employerId"
							render={({ field }) => (
								<FormItem>
									<FormLabel>מעסיק</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>שם</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>תיאור</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="effectiveFrom"
							render={({ field }) => (
								<FormItem>
									<FormLabel>תאריך התחלה</FormLabel>
									<FormControl>
										<Input type="date" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="effectiveTo"
							render={({ field }) => (
								<FormItem>
									<FormLabel>תאריך סיום</FormLabel>
									<FormControl>
										<Input type="date" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<Button type="submit" disabled={loading} className="w-full">
							{isEdit ? "שמירה" : "יצירה"}
						</Button>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
