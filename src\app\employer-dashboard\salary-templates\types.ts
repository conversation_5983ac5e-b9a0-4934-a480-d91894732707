export interface SalaryTemplateComponent {
  id: string;
  templateId: string;
  componentType: 'PAYMENT' | 'VALUE' | 'DEDUCTION';
  componentCode: string;
  componentName: string;
  calculationType?: string | null;
  fixedAmount?: number | null;
  percentage?: number | null;
  formula?: string | null;
  isRequired: boolean;
  isActive: boolean;
}

export interface SalaryTemplate {
  id: string;
  employerId: string;
  name: string;
  description?: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  SalaryTemplateComponent: SalaryTemplateComponent[];
}

export interface SalaryTemplateFormData {
  employerId: string;
  name: string;
  description?: string;
  components: {
    componentType: 'PAYMENT' | 'VALUE' | 'DEDUCTION';
    componentCode: string;
    componentName: string;
  }[];
}
