"use client";

import { DetailView } from "../../components/detail-view";
import { api } from "@/trpc/react";
import type { Association } from "../hooks";
import { format } from "date-fns";
import { he } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, User, Building, Briefcase, FileText, Clock } from "lucide-react";

interface AssociationDetailProps {
	associationId: string;
	employerId: string;
}

export function AssociationDetail({ associationId, employerId }: AssociationDetailProps) {
	// Fetch association details
	const { data: association, isLoading } = api.associations.getById.useQuery(
		associationId,
		{
			enabled: !!associationId
		}
	);

	if (isLoading) {
		return (
			<DetailView itemId={associationId} title="טוען...">
				<div className="animate-pulse space-y-4">
					<div className="h-4 bg-gray-200 rounded w-3/4"></div>
					<div className="h-4 bg-gray-200 rounded w-1/2"></div>
				</div>
			</DetailView>
		);
	}

	if (!association) {
		return (
			<DetailView itemId={associationId} title="שגיאה">
				<p className="text-red-500">לא נמצאו פרטי השיוך</p>
			</DetailView>
		);
	}

	const getAssociationTypeLabel = (type: string) => {
		switch (type) {
			case "DEPARTMENT":
				return "מחלקה";
			case "ROLE":
				return "תפקיד";
			case "SALARY_TEMPLATE":
				return "תבנית שכר";
			case "SALARY_AGREEMENT":
				return "הסכם שכר";
			default:
				return type;
		}
	};

	const getAssociationIcon = (type: string) => {
		switch (type) {
			case "DEPARTMENT":
				return <Building className="h-4 w-4" />;
			case "ROLE":
				return <Briefcase className="h-4 w-4" />;
			case "SALARY_TEMPLATE":
			case "SALARY_AGREEMENT":
				return <FileText className="h-4 w-4" />;
			default:
				return null;
		}
	};

	return (
		<DetailView itemId={associationId} title="פרטי שיוך">
			<div className="space-y-4">
				{/* Association Type Badge */}
				<div className="flex items-center gap-2">
					{getAssociationIcon(association.associationType)}
					<Badge variant="outline">
						{getAssociationTypeLabel(association.associationType)}
					</Badge>
				</div>

				{/* Employee Info */}
				<Card>
					<CardHeader className="pb-3">
						<CardTitle className="text-base flex items-center gap-2">
							<User className="h-4 w-4" />
							פרטי עובד
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-2">
						<div>
							<span className="text-sm text-gray-500">שם:</span>
							<p className="font-medium">{association.employeeName}</p>
						</div>
						<div>
							<span className="text-sm text-gray-500">מזהה:</span>
							<p className="font-mono text-sm">{association.employeeId}</p>
						</div>
					</CardContent>
				</Card>

				{/* Associated Entity Info */}
				<Card>
					<CardHeader className="pb-3">
						<CardTitle className="text-base flex items-center gap-2">
							{getAssociationIcon(association.associationType)}
							פרטי {getAssociationTypeLabel(association.associationType)}
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-2">
						<div>
							<span className="text-sm text-gray-500">שם:</span>
							<p className="font-medium">{association.associatedEntityName}</p>
						</div>
						<div>
							<span className="text-sm text-gray-500">מזהה:</span>
							<p className="font-mono text-sm">{association.associatedEntityId}</p>
						</div>
					</CardContent>
				</Card>

				{/* Date Range */}
				<Card>
					<CardHeader className="pb-3">
						<CardTitle className="text-base flex items-center gap-2">
							<Calendar className="h-4 w-4" />
							תקופת שיוך
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-2">
						<div>
							<span className="text-sm text-gray-500">תאריך התחלה:</span>
							<p className="font-medium">
								{format(new Date(association.startDate), "dd/MM/yyyy", { locale: he })}
							</p>
						</div>
						{association.endDate && (
							<div>
								<span className="text-sm text-gray-500">תאריך סיום:</span>
								<p className="font-medium">
									{format(new Date(association.endDate), "dd/MM/yyyy", { locale: he })}
								</p>
							</div>
						)}
						<div className="pt-2">
							<Badge variant={association.endDate ? "secondary" : "default"}>
								{association.endDate ? "הסתיים" : "פעיל"}
							</Badge>
						</div>
					</CardContent>
				</Card>

				{/* Metadata */}
				<Card>
					<CardHeader className="pb-3">
						<CardTitle className="text-base flex items-center gap-2">
							<Clock className="h-4 w-4" />
							מידע נוסף
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-2 text-sm">
						<div>
							<span className="text-gray-500">נוצר בתאריך:</span>
							<p>{format(new Date(association.createdAt), "dd/MM/yyyy HH:mm", { locale: he })}</p>
						</div>
						<div>
							<span className="text-gray-500">עודכן לאחרונה:</span>
							<p>{format(new Date(association.updatedAt), "dd/MM/yyyy HH:mm", { locale: he })}</p>
						</div>
					</CardContent>
				</Card>

				{/* Actions */}
				<div className="flex gap-2 pt-4">
					<button className="flex-1 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
						עריכה
					</button>
					<button className="flex-1 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
						מחיקה
					</button>
				</div>
			</div>
		</DetailView>
	);
} 