/**
 * Associations Hooks
 * 
 * This file contains hooks for managing associations between employees and other entities
 * (departments, roles, salary templates, and salary agreements).
 * 
 * IMPLEMENTATION STATUS:
 * 
 * 1. COMPLETED: Basic CRUD hooks for associations
 * 2. COMPLETED: Data fetching hooks for related entities
 * 3. COMPLETED: Type definitions for associations and form values
 * 
 * TO FINISH IMPLEMENTATION:
 * 
 * 1. Run proper Prisma migrations to apply schema changes
 *    - npx prisma migrate dev --name add_associations
 *    - npx prisma generate
 * 
 * 2. Resolve permission issues with Prisma client generation
 *    - May require restarting the application or using a different environment
 * 
 * 3. Test hooks with the actual database after migrations are complete
 */

import { useState } from "react";
import { format, parseISO } from "date-fns";
import { api } from "@/trpc/react";

// Types for associations
export type AssociationType = 
  | "DEPARTMENT" 
  | "ROLE" 
  | "SALARY_TEMPLATE" 
  | "SALARY_AGREEMENT";

export type Association = {
  id: string;
  employeeId: string;
  employeeName: string;
  employeeIdentifier: string;
  associationType: AssociationType;
  associatedEntityId: string;
  associatedEntityName: string;
  startDate: string;
  endDate: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
};

export type AssociationFormValues = {
  employeeId: string;
  associationType: AssociationType;
  associatedEntityId: string;
  startDate: string;
  endDate?: string;
  notes?: string;
};

// Hook to fetch all associations
export function useAssociations(
  page = 1,
  employerId: string,
  associationType?: AssociationType,
  searchTerm?: string,
  startDate?: string,
  endDate?: string
) {
  return api.associations.getAll.useQuery(
    {
      employerId,
      page,
      associationType,
      searchTerm,
      startDate,
      endDate,
    },
    {
      enabled: !!employerId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Hook to create a new association
export function useCreateAssociation(employerId: string) {
  const utils = api.useUtils();
  
  return api.associations.create.useMutation({
    onSuccess: () => {
      utils.associations.getAll.invalidate({ employerId });
    }
  });
}

// Hook to update an existing association
export function useUpdateAssociation(employerId: string) {
  const utils = api.useUtils();
  
  return api.associations.update.useMutation({
    onSuccess: () => {
      utils.associations.getAll.invalidate({ employerId });
    }
  });
}

// Hook to delete an association
export function useDeleteAssociation(employerId: string) {
  const utils = api.useUtils();
  
  return api.associations.delete.useMutation({
    onSuccess: () => {
      utils.associations.getAll.invalidate({ employerId });
    }
  });
}

// Hook to generate an associations changes report
export function useAssociationsChangesReport(employerId: string) {
  return api.associations.generateReport.useMutation();
}

// Hook to fetch association history for a specific association
export function useAssociationHistory(associationId: string, employerId: string) {
  return api.associations.getHistory.useQuery(
    { associationId, employerId },
    {
      enabled: !!associationId && !!employerId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Hook to manage the association form state
export function useAssociationForm(initialValues?: Partial<AssociationFormValues>) {
  const [formValues, setFormValues] = useState<Partial<AssociationFormValues>>(
    initialValues || {
      associationType: "DEPARTMENT",
      startDate: format(new Date(), "yyyy-MM-dd")
    }
  );

  const handleChange = (field: keyof AssociationFormValues, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setFormValues(initialValues || {
      associationType: "DEPARTMENT",
      startDate: format(new Date(), "yyyy-MM-dd")
    });
  };

  return {
    formValues,
    handleChange,
    resetForm
  };
}

// Utility to format dates for display
export function formatDateString(dateStr: string | null | undefined) {
  if (!dateStr) return "";
  try {
    return format(parseISO(dateStr), "dd/MM/yyyy");
  } catch (error) {
    return dateStr;
  }
}

// Hook to fetch departments for an employer
export function useDepartments(employerId: string) {
  return api.associations.getDepartments.useQuery(employerId, {
    enabled: !!employerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch employee roles for an employer
export function useEmployeeRoles(employerId: string) {
  return api.associations.getEmployeeRoles.useQuery(employerId, {
    enabled: !!employerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch salary templates for an employer
export function useSalaryTemplates(employerId: string) {
  return api.associations.getSalaryTemplates.useQuery(employerId, {
    enabled: !!employerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch salary agreements for an employer
export function useSalaryAgreements(employerId: string) {
  return api.associations.getSalaryAgreements.useQuery(employerId, {
    enabled: !!employerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch employees for an employer
export function useEmployees(employerId: string) {
  return api.associations.getEmployees.useQuery(employerId, {
    enabled: !!employerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Define the history entry type directly here
export interface AssociationHistoryEntry {
  id: string;
  changeType: "CREATE" | "UPDATE" | "DELETE";
  changeDate: string;
  employeeName: string;
  associationType: string;
  entityName: string;
  oldValue?: string;
  newValue?: string;
  changedBy: string;
} 