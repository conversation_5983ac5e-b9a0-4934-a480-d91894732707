import {
	Building,
	Calculator,
	Clock,
	FileText,
	Home,
	Link2,
	Receipt,
	Settings,
	Users,
} from "lucide-react";
import { motion } from "framer-motion";
import Link from "next/link";

const container = {
	hidden: { opacity: 0 },
	show: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
		},
	},
};

const item = {
	hidden: { opacity: 0, x: 20 }, // Changed from -20 to 20 for RTL
	show: {
		opacity: 1,
		x: 0,
		transition: {
			type: "spring",
			stiffness: 100,
			damping: 10,
		},
	},
};

export const employerDashboardNavItems = [
        {
                title: "לוח בקרה",
                href: "/employer-dashboard",
                icon: <Home className="h-4 w-4" />,
        },
        {
                title: "שיוכים",
                href: "/employer-dashboard/associations",
                icon: <Link2 className="h-4 w-4" />,
        },
        {
                title: "הסכמי נוכחות",
                href: "/employer-dashboard/attendance-agreements",
                icon: <Clock className="h-4 w-4" />,
        },
        {
                title: "הסכמי שכר",
                href: "/employer-dashboard/salary-agreements",
                icon: <Calculator className="h-4 w-4" />,
        },
        {
                title: "תלושי שכר",
                href: "/employer-dashboard/payslips",
                icon: <Receipt className="h-4 w-4" />,
        },
        {
                title: "תבניות שכר",
                href: "/employer-dashboard/salary-templates",
                icon: <Calculator className="h-4 w-4" />,
        },
        {
                title: "מרכיבי תשלום",
                href: "/employer-dashboard/payment-components",
                icon: <Calculator className="h-4 w-4" />,
        },
        {
                title: "מרכיבי ניכוי",
                href: "/employer-dashboard/deduction-components",
                icon: <Calculator className="h-4 w-4" />,
        },
        {
                title: "נוסחאות",
                href: "/employer-dashboard/formulas",
                icon: <Calculator className="h-4 w-4" />,
        },
        {
                title: "טפסים",
                href: "/employer-dashboard/forms",
                icon: <FileText className="h-4 w-4" />,
        },
];

export const AnimatedNavigation = () => {
	return (
		<motion.nav
			variants={container}
			initial="hidden"
			animate="show"
			dir="rtl"
			className="space-y-2"
		>
			{employerDashboardNavItems.map((navItem) => (
				<motion.div key={navItem.href} variants={item}>
					<Link
						href={navItem.href}
						className="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900 hover:bg-gray-100"
					>
						{navItem.icon}
						<span className="font-normal">{navItem.title}</span>
					</Link>
				</motion.div>
			))}
		</motion.nav>
	);
};
