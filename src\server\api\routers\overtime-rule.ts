import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";

// Input validation schemas
const createOvertimeRuleSchema = z.object({
  agreementId: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  fromHour: z.number().min(0).max(24),
  toHour: z.number().min(0).max(24).optional(),
  rate: z.number().min(1).max(5),
  dayType: z.enum(["WEEKDAY", "WEEKEND", "HOLIDAY"]),
  priority: z.number().min(0).default(0),
});

const updateOvertimeRuleSchema = createOvertimeRuleSchema.extend({
  id: z.string(),
});

export const overtimeRuleRouter = createTRPCRouter({
  // Get all overtime rules
  getAll: publicProcedure
    .input(
      z.object({
        agreementId: z.string().optional(),
      }).optional()
    )
    .query(async ({ ctx, input }) => {
      const where = input?.agreementId 
        ? { agreementId: input.agreementId, deletedAt: null }
        : { deletedAt: null };
      
      return ctx.db.overtimeRule.findMany({
        where,
        orderBy: [
          { dayType: "asc" },
          { priority: "asc" },
          { fromHour: "asc" },
        ],
      });
    }),

  // Get overtime rule by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const rule = await ctx.db.overtimeRule.findUnique({
        where: { id: input.id },
      });

      if (!rule || rule.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "חוק שעות נוספות לא נמצא",
        });
      }

      return rule;
    }),
  // Create new overtime rule
  create: protectedProcedure
    .input(createOvertimeRuleSchema)
    .mutation(async ({ ctx, input }) => {
      // Validate hour range
      if (input.toHour && input.toHour <= input.fromHour) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "שעת סיום חייבת להיות גדולה משעת התחלה",        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      return ctx.db.overtimeRule.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update overtime rule
  update: publicProcedure
    .input(updateOvertimeRuleSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // Check if rule exists
      const existing = await ctx.db.overtimeRule.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "חוק שעות נוספות לא נמצא",
        });
      }

      // Validate hour range
      if (data.toHour && data.toHour <= data.fromHour) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "שעת סיום חייבת להיות גדולה משעת התחלה",
        });
      }

      return ctx.db.overtimeRule.update({
        where: { id },
        data,
      });
    }),

  // Delete overtime rule (soft delete)
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if rule exists
      const existing = await ctx.db.overtimeRule.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "חוק שעות נוספות לא נמצא",
        });
      }

      return ctx.db.overtimeRule.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
}); 