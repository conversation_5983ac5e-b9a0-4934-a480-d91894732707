import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

// Input validation schemas
const createShiftSchema = z.object({
  agreementId: z.string(),
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  breakMinutes: z.number().min(0),
  isNightShift: z.boolean().default(false),
  isFlexible: z.boolean().default(false),
  color: z.string().optional(),
});

const updateShiftSchema = createShiftSchema.extend({
  id: z.string(),
});

export const shiftRouter = createTRPCRouter({
  // Get all shifts
  getAll: publicProcedure
    .input(
      z.object({
        agreementId: z.string().optional(),
      }).optional()
    )
    .query(async ({ ctx, input }) => {
      const where = input?.agreementId 
        ? { agreementId: input.agreementId, deletedAt: null }
        : { deletedAt: null };
      
      return ctx.db.shift.findMany({
        where,
        orderBy: {
          startTime: "asc",
        },
      });
    }),

  // Get shift by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const shift = await ctx.db.shift.findUnique({
        where: { id: input.id },
      });

      if (!shift || shift.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "משמרת לא נמצאה",
        });
      }

      return shift;
    }),
  // Create new shift
  create: protectedProcedure
    .input(createShiftSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if code already exists for this agreement
      const existing = await ctx.db.shift.findFirst({
        where: {
          agreementId: input.agreementId,
          code: input.code,
          deletedAt: null,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קוד משמרת כבר קיים בהסכם זה",
        });
      }

      // Validate time range
      if (input.startTime === input.endTime) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "שעת התחלה וסיום לא יכולות להיות זהות",
        });
      }      // Determine tenant from authenticated session
      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      return ctx.db.shift.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update shift
  update: publicProcedure
    .input(updateShiftSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // Check if shift exists
      const existing = await ctx.db.shift.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "משמרת לא נמצאה",
        });
      }

      // Check if new code conflicts with another shift
      if (data.code !== existing.code) {
        const codeExists = await ctx.db.shift.findFirst({
          where: {
            agreementId: data.agreementId,
            code: data.code,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (codeExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "קוד משמרת כבר קיים בהסכם זה",
          });
        }
      }

      return ctx.db.shift.update({
        where: { id },
        data,
      });
    }),

  // Delete shift (soft delete)
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if shift exists
      const existing = await ctx.db.shift.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "משמרת לא נמצאה",
        });
      }

      return ctx.db.shift.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),

  // Validate shift overlap
  validateOverlap: publicProcedure
    .input(
      z.object({
        agreementId: z.string(),
        startTime: z.string(),
        endTime: z.string(),
        excludeShiftId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const shifts = await ctx.db.shift.findMany({
        where: {
          agreementId: input.agreementId,
          id: input.excludeShiftId ? { not: input.excludeShiftId } : undefined,
          deletedAt: null,
        },
      });

      const conflicts = shifts.filter(shift => {
        // Convert times to minutes for easier comparison
        const toMinutes = (time: string) => {
          const parts = time.split(':').map(Number);
          const hours = parts[0] || 0;
          const minutes = parts[1] || 0;
          return hours * 60 + minutes;
        };

        const inputStart = toMinutes(input.startTime);
        const inputEnd = toMinutes(input.endTime);
        const shiftStart = toMinutes(shift.startTime);
        const shiftEnd = toMinutes(shift.endTime);

        // Handle overnight shifts
        const inputOvernight = inputEnd < inputStart;
        const shiftOvernight = shiftEnd < shiftStart;

        if (inputOvernight || shiftOvernight) {
          // Complex overnight logic - simplified for now
          return true;
        }

        // Check for overlap
        return (
          (inputStart >= shiftStart && inputStart < shiftEnd) ||
          (inputEnd > shiftStart && inputEnd <= shiftEnd) ||
          (inputStart <= shiftStart && inputEnd >= shiftEnd)
        );
      });

      return {
        isValid: conflicts.length === 0,
        conflicts: conflicts.map(shift => ({
          shiftId: shift.id,
          shiftName: shift.name,
          startTime: shift.startTime,
          endTime: shift.endTime,
        })),
      };
    }),
}); 