import { describe, it, expect } from 'vitest';
import { PayslipItemKod } from '@prisma/client';
import { sumContributionTotals } from '../payslip';

describe('sumContributionTotals', () => {
  it('calculates totals from payslip items', () => {
    const res = sumContributionTotals([
      { kod: PayslipItemKod.K_PENSION_EMP, amount: 100 },
      { kod: PayslipItemKod.K_0313, amount: 200 },
      { kod: PayslipItemKod.K_0250, amount: 300 },
      { kod: PayslipItemKod.K_PENSION_EMP, amount: 50 },
    ]);
    expect(res).toEqual({
      pensionEmployee: 150,
      pensionEmployer: 200,
      severancePay: 300,
    });
  });
});
