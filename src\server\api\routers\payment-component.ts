import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import type { Prisma } from "@prisma/client";

// Input validation schemas
const paymentTypeEnum = z.enum([
  "ALLOWANCE",
  "EXPENSE",
  "REIMBURSEMENT",
  "CASH_REDEMPTION",
  "OVERTIME",
  "OTHER",
]);

const paymentGroupEnum = z.enum(["BASIC", "SUPPLEMENT"]);

const taxCalculationEnum = z.enum(["TAX_LIABLE", "TAX_EXEMPT"]);
const socialSecurityEnum = z.enum(["SS_LIABLE", "SS_EXEMPT"]);

const createPaymentComponentSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  paymentType: paymentTypeEnum,
  paymentGroup: paymentGroupEnum.optional(),
  taxCalculation: taxCalculationEnum.optional(),
  socialSecurityCalculation: socialSecurityEnum.optional(),
  affectsPension: z.boolean().default(false),
  isOneTime: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

const updatePaymentComponentSchema = createPaymentComponentSchema.extend({
  id: z.string(),
});

const filtersSchema = z.object({
  search: z.string().optional(),
  paymentType: paymentTypeEnum.optional(),
  paymentGroup: paymentGroupEnum.optional(),
  isActive: z.boolean().optional(),
  isOneTime: z.boolean().optional(),
}).optional();

export const paymentComponentRouter = createTRPCRouter({
  // Get all payment components
  getAll: publicProcedure
    .input(filtersSchema)
    .query(async ({ ctx, input }) => {
      const where: Prisma.PaymentComponentWhereInput = { deletedAt: null };
      
      if (input?.search) {
        where.OR = [
          { name: { contains: input.search, mode: "insensitive" } },
          { code: { contains: input.search, mode: "insensitive" } },
          { description: { contains: input.search, mode: "insensitive" } },
        ];
      }
      
      if (input?.paymentType) {
        where.paymentType = input.paymentType;
      }

      if (input?.paymentGroup) {
        where.paymentGroup = input.paymentGroup;
      }
      
      if (input?.isActive !== undefined) {
        where.isActive = input.isActive;
      }
      
      if (input?.isOneTime !== undefined) {
        where.isOneTime = input.isOneTime;
      }
      
      return ctx.db.paymentComponent.findMany({
        where,
        orderBy: { name: "asc" },
      });
    }),

  // Get payment component by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const component = await ctx.db.paymentComponent.findUnique({
        where: { id: input.id },
      });

      if (!component || component.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב תשלום לא נמצא",
      });
      }      return component;
    }),

  // Create new payment component
  create: protectedProcedure
    .input(createPaymentComponentSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if code already exists
      const existing = await ctx.db.paymentComponent.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קוד רכיב תשלום כבר קיים במערכת",        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      return ctx.db.paymentComponent.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update payment component
  update: publicProcedure
    .input(updatePaymentComponentSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // Check if component exists
      const existing = await ctx.db.paymentComponent.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ניכוי לא נמצא",
        });
      }

      // Check if new code conflicts with another component
      if (data.code !== existing.code) {
        const codeExists = await ctx.db.paymentComponent.findFirst({
          where: {
            code: data.code,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (codeExists) {
          throw new TRPCError({
            code: "CONFLICT",
          message: "קוד רכיב תשלום כבר קיים במערכת",
          });
        }
      }

      return ctx.db.paymentComponent.update({
        where: { id },
        data,
      });
    }),

  // Delete payment component (soft delete)
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if component exists
      const existing = await ctx.db.paymentComponent.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב תשלום לא נמצא",
        });
      }

      // For now, skip checking if component is in use since PayslipItem doesn't have paymentComponentId
      // TODO: Update this once the proper relation is established
      // const inUse = await ctx.db.payslipItem.findFirst({
      //   where: {
      //     paymentComponentId: input.id,
      //     deletedAt: null,
      //   },
      // });

      // if (inUse) {
      //   throw new TRPCError({
      //     code: "PRECONDITION_FAILED",
      //     message: "לא ניתן למחוק רכיב תשלום שנמצא בשימוש בתלושי שכר",
      //   });
      // }

      return ctx.db.paymentComponent.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
  // Duplicate payment component
  duplicate: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const original = await ctx.db.paymentComponent.findUnique({
        where: { id: input.id },
      });

      if (!original || original.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב תשלום לא נמצא",        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      // Generate new code
      let newCode = `${original.code}_COPY`;
      let counter = 1;
      
      while (true) {
        const exists = await ctx.db.paymentComponent.findFirst({
          where: {
            code: newCode,
            deletedAt: null,
          },
        });
        
        if (!exists) break;
        
        counter++;
        newCode = `${original.code}_COPY${counter}`;
      }

      return ctx.db.paymentComponent.create({
        data: {
          ...original,
          id: undefined,
          code: newCode,
          name: `${original.name} (עותק)`,
          createdAt: undefined,
          updatedAt: undefined,
          tenantId,
        },
      });
    }),

  // Validate payment component
  validate: publicProcedure
    .input(createPaymentComponentSchema)
    .query(async ({ ctx, input }) => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check for duplicate code
      const existingCode = await ctx.db.paymentComponent.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existingCode) {
        errors.push("קוד רכיב תשלום כבר קיים במערכת");
      }

      // Check for similar names
      const similarName = await ctx.db.paymentComponent.findFirst({
        where: {
          name: {
            equals: input.name,
            mode: "insensitive",
          },
          deletedAt: null,
        },
      });

      if (similarName) {
        warnings.push("קיים רכיב תשלום עם שם דומה");
      }

      // Validate percentage

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    }),

  // Get usage statistics
  getUsageStats: publicProcedure
    .query(async ({ ctx }) => {
      const components = await ctx.db.paymentComponent.findMany({
        where: { deletedAt: null },
      });

      // For now, return default values since we don't have payslipItems relation
      return components.map(component => ({
        paymentComponentId: component.id,
        paymentComponent: component,
        usageCount: 0, // Default value as we don't have the relation yet
        activeEmployees: 0, // TODO: Calculate from payslip items
        totalAmount: 0, // TODO: Calculate from payslip items
        lastUsed: null, // TODO: Get from payslip items
      }));
    }),

  // Get change report
  getChangeReport: publicProcedure
    .input(z.object({
      fromDate: z.date(),
      toDate: z.date(),
    }))
    .query(async ({ ctx, input }) => {
      // TODO: Implement audit log tracking
      return {
        fromDate: input.fromDate,
        toDate: input.toDate,
        changes: [],
        summary: {
          totalChanges: 0,
          createdCount: 0,
          updatedCount: 0,
          deletedCount: 0,
        },
      };
    }),

  // Bulk update
  bulkUpdate: publicProcedure
    .input(z.object({
      ids: z.array(z.string()),
      updates: z.object({
        isActive: z.boolean().optional(),
        paymentGroup: paymentGroupEnum.optional(),
        taxCalculation: taxCalculationEnum.optional(),
        socialSecurityCalculation: socialSecurityEnum.optional(),
      }),
    }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.db.paymentComponent.updateMany({
        where: {
          id: { in: input.ids },
          deletedAt: null,
        },
        data: input.updates,
      });

      return {
        updated: result.count,
      };
    }),

  // Export payment components
  export: publicProcedure
    .mutation(async ({ ctx }) => {
      const components = await ctx.db.paymentComponent.findMany({
        where: { deletedAt: null },
        orderBy: { name: "asc" },
      });

      return components;
    }),
  // Import payment components
  import: protectedProcedure
    .input(z.array(createPaymentComponentSchema))
    .mutation(async ({ ctx, input }) => {
      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      let imported = 0;

      for (const component of input) {
        // Check if code already exists
        const existing = await ctx.db.paymentComponent.findFirst({
          where: {
            code: component.code,
            deletedAt: null,
          },
        });

        if (!existing) {
          await ctx.db.paymentComponent.create({
            data: {
              ...component,
              tenantId,
            },
          });
          imported++;
        }
      }

      return {
        imported,
        total: input.length,
      };
    }),
}); 
