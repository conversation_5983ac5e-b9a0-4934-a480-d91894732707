// ============================================
// Payment Component Types
// ============================================

export interface PaymentComponent {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  paymentType: PaymentType;
  taxCalculation: TaxCalculationType;
  socialSecurityCalculation: SocialSecurityCalculationType;
  affectsPension: boolean;
  paymentGroup?: PaymentGroup | null;
  isOneTime: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

// Enums for payment types
export enum PaymentType {
  ALLOWANCE = "ALLOWANCE",
  EXPENSE = "EXPENSE",
  REIMBURSEMENT = "REIMBURSEMENT",
  CASH_REDEMPTION = "CASH_REDEMPTION",
  OVERTIME = "OVERTIME",
  OTHER = "OTHER",
}

export enum TaxCalculationType {
  TAX_LIABLE = "TAX_LIABLE",     // חייב מ"ה
  TAX_EXEMPT = "TAX_EXEMPT",      // פטור מ"ה
}

export enum SocialSecurityCalculationType {
  SS_LIABLE = "SS_LIABLE",        // חייב ב"ל
  SS_EXEMPT = "SS_EXEMPT",        // פטור ב"ל
}

export enum PaymentGroup {
  BASIC = "BASIC",
  SUPPLEMENT = "SUPPLEMENT",
}

// ============================================
// Form Types
// ============================================

export interface PaymentComponentFormData {
  code: string;
  name: string;
  description?: string;
  paymentType: PaymentType;
  taxCalculation: TaxCalculationType;
  socialSecurityCalculation: SocialSecurityCalculationType;
  affectsPension: boolean;
  paymentGroup?: PaymentGroup;
  isOneTime: boolean;
  isActive: boolean;
}

// ============================================
// Filter and Search Types
// ============================================

export interface PaymentComponentFilters {
  search?: string;
  paymentType?: PaymentType;
  paymentGroup?: PaymentGroup;
  isActive?: boolean;
  isOneTime?: boolean;
}

// ============================================
// Report Types
// ============================================

export interface PaymentChangeLog {
  id: string;
  paymentComponentId: string;
  paymentComponent?: PaymentComponent;
  changeType: "CREATE" | "UPDATE" | "DELETE";
  changeDetails: Record<string, any>;
  changedBy: string;
  changedAt: Date;
}

export interface PaymentChangeReport {
  fromDate: Date;
  toDate: Date;
  changes: PaymentChangeLog[];
  summary: {
    totalChanges: number;
    createdCount: number;
    updatedCount: number;
    deletedCount: number;
  };
}

// ============================================
// Usage Types
// ============================================

export interface PaymentUsage {
  paymentComponentId: string;
  paymentComponent?: PaymentComponent;
  usageCount: number;
  activeEmployees: number;
  totalAmount: number;
  lastUsed?: Date;
}

// ============================================
// Validation Types
// ============================================

export interface PaymentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ============================================
// Constants
// ============================================

export const PAYMENT_TYPE_LABELS: Record<PaymentType, string> = {
  [PaymentType.ALLOWANCE]: "תוספת",
  [PaymentType.EXPENSE]: "החזר הוצאה",
  [PaymentType.REIMBURSEMENT]: "שיפוי",
  [PaymentType.CASH_REDEMPTION]: "פדיון מזומן",
  [PaymentType.OVERTIME]: "שעות נוספות",
  [PaymentType.OTHER]: "אחר",
};

export const TAX_CALCULATION_LABELS: Record<TaxCalculationType, string> = {
  [TaxCalculationType.TAX_LIABLE]: "חייב מ\"ה",
  [TaxCalculationType.TAX_EXEMPT]: "פטור מ\"ה",
};

export const SOCIAL_SECURITY_LABELS: Record<SocialSecurityCalculationType, string> = {
  [SocialSecurityCalculationType.SS_LIABLE]: "חייב ב\"ל",
  [SocialSecurityCalculationType.SS_EXEMPT]: "פטור ב\"ל",
};

export const PAYMENT_GROUP_LABELS: Record<PaymentGroup, string> = {
  [PaymentGroup.BASIC]: "בסיס",
  [PaymentGroup.SUPPLEMENT]: "תוספת",
};
