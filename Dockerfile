FROM node:20-alpine

WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@10.11.0

# Copy package files
COPY package.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy the rest of the application
COPY . .

# Generate Prisma client
RUN pnpm postinstall

# Set environment variable to skip generating the Prisma client again at runtime
ENV SKIP_PRISMA_GENERATE=true

# Set the default command
CMD ["pnpm", "dev"] 