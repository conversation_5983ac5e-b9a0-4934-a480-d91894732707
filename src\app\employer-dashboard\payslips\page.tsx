"use client";

import { useState, useRef } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Search, Download, Filter, Eye, Send, FileText, Printer, Calendar, DollarSign } from "lucide-react";
import { api } from "@/trpc/react";
import { format } from "date-fns";
import { he } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useSession } from "next-auth/react";
import "./print-styles.css";

export default function PayslipsPage() {
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPayslip, setSelectedPayslip] = useState<string | null>(null);
  const printRef = useRef<HTMLDivElement>(null);

  // Get current session
  const { data: session } = useSession();

  // API queries
  const { data: departmentsData } = api.department.getAll.useQuery({
    employerId: session?.user?.employerId || "",
    page: 1,
    limit: 100,
  }, {
    enabled: !!session?.user?.employerId
  });
  const { data: payslipsData, isLoading } = api.payslip.getAll.useQuery({
    year: selectedYear,
    month: selectedMonth,
  });

  const { data: selectedPayslipData } = api.payslip.getById.useQuery(
    selectedPayslip!,
    { enabled: !!selectedPayslip }
  );

  // Print handler
  const handlePrint = () => {
    if (!printRef.current) return;
    const printContent = printRef.current;
    const originalContents = document.body.innerHTML;
    
    document.body.innerHTML = printContent.innerHTML;
    window.print();
    document.body.innerHTML = originalContents;
    window.location.reload(); // Reload to restore React state
  };

  const months = [
    { value: 1, label: "ינואר" },
    { value: 2, label: "פברואר" },
    { value: 3, label: "מרץ" },
    { value: 4, label: "אפריל" },
    { value: 5, label: "מאי" },
    { value: 6, label: "יוני" },
    { value: 7, label: "יולי" },
    { value: 8, label: "אוגוסט" },
    { value: 9, label: "ספטמבר" },
    { value: 10, label: "אוקטובר" },
    { value: 11, label: "נובמבר" },
    { value: 12, label: "דצמבר" }
  ];

  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i);

  const formatCurrency = (amount: number | string | null | undefined | { toString(): string }) => {
    if (!amount) return "₪0";
    const num = typeof amount === 'string' ? parseFloat(amount) : 
                typeof amount === 'number' ? amount :
                parseFloat(amount.toString());
    return new Intl.NumberFormat("he-IL", {
      style: "currency",
      currency: "ILS",
    }).format(num);
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      DRAFT: { label: "טיוטה", variant: "secondary" as const },
      CALCULATED: { label: "מחושב", variant: "outline" as const },
      APPROVED: { label: "מאושר", variant: "default" as const },
      SENT: { label: "נשלח", variant: "default" as const },
      PAID: { label: "שולם", variant: "default" as const }
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: "secondary" as const };
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  // Filter payslips based on search and department
  const filteredPayslips = payslipsData?.payslips?.filter(payslip => {
    const matchesSearch = !searchQuery || 
      `${payslip.employee.firstName} ${payslip.employee.lastName}`.includes(searchQuery) ||
      payslip.employee.nationalId.includes(searchQuery);
    
    // For now, we'll skip department filtering since it's not in the payslip data
    return matchesSearch;
  });

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">תלושי שכר</h1>
          <p className="text-gray-600 mt-1">ניהול וצפייה בתלושי שכר של עובדים</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 ml-2" />
            סינון מתקדם
          </Button>
          <Button>
            <FileText className="h-4 w-4 ml-2" />
            יצירת תלושים
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">סה״כ תלושים</p>
                <p className="text-2xl font-bold">{payslipsData?.total || 0}</p>
              </div>
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">ממתינים לאישור</p>
                <p className="text-2xl font-bold">
                  {payslipsData?.payslips?.filter(p => p.status === "CALCULATED").length || 0}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">אושרו החודש</p>
                <p className="text-2xl font-bold">
                  {payslipsData?.payslips?.filter(p => p.status === "APPROVED").length || 0}
                </p>
              </div>
              <FileText className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">סה״כ לתשלום</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    payslipsData?.payslips?.reduce((sum, p) => sum + Number(p.netPay), 0) || 0
                  )}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="py-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select value={selectedMonth.toString()} onValueChange={(v) => setSelectedMonth(Number(v))}>
              <SelectTrigger>
                <SelectValue placeholder="בחר חודש" />
              </SelectTrigger>
              <SelectContent>
                {months.map((month) => (
                  <SelectItem key={month.value} value={month.value.toString()}>
                    {month.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedYear.toString()} onValueChange={(v) => setSelectedYear(Number(v))}>
              <SelectTrigger>
                <SelectValue placeholder="בחר שנה" />
              </SelectTrigger>
              <SelectContent>
                {years.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
              <SelectTrigger>
                <SelectValue placeholder="בחר מחלקה" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">כל המחלקות</SelectItem>
                {departmentsData?.departments?.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id}>
                    {dept.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="חיפוש לפי שם עובד..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payslips List */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>רשימת תלושים - {months.find(m => m.value === selectedMonth)?.label} {selectedYear}</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">טוען...</div>
              ) : !filteredPayslips || filteredPayslips.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  לא נמצאו תלושי שכר לתקופה שנבחרה
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredPayslips.map((payslip) => (
                    <div
                      key={payslip.id}
                      className={cn(
                        "p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md",
                        selectedPayslip === payslip.id 
                          ? "bg-blue-50 border-blue-400 shadow-md" 
                          : "hover:bg-gray-50"
                      )}
                      onClick={() => setSelectedPayslip(payslip.id)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">
                            {payslip.employee.firstName} {payslip.employee.lastName}
                          </h3>
                          <div className="flex items-center gap-4 mt-1">
                            <p className="text-sm text-gray-600">
                              <span className="font-medium">ת.ז:</span> {payslip.employee.nationalId}
                            </p>
                            <p className="text-sm text-gray-600">
                              <span className="font-medium">תקופה:</span> {payslip.month}/{payslip.year}
                            </p>
                          </div>
                        </div>
                        <div className="text-left space-y-2">
                          <div>{getStatusBadge(payslip.status)}</div>
                          <div className="text-left">
                            <p className="text-xs text-gray-500">שכר נטו</p>
                            <p className="text-lg font-bold text-blue-600">{formatCurrency(payslip.netPay)}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Payslip Preview */}
        <div className="space-y-4">
          {selectedPayslipData ? (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>תצוגה מקדימה</CardTitle>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={handlePrint}>
                      <Printer className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button size="sm">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div ref={printRef} id="printable-payslip" className="bg-white p-6 space-y-4 print:p-8">
                    {/* Company Header */}
                    <div className="text-center border-b pb-4">
                      <h2 className="text-2xl font-bold">
                        {session?.user?.employerName || "חברה בע״מ"}
                      </h2>
                      <p className="text-sm text-gray-600 mt-1">
                        תלוש שכר לחודש {months.find(m => m.value === selectedPayslipData.month)?.label} {selectedPayslipData.year}
                      </p>
                    </div>

                    {/* Employee Details - Two columns */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="font-medium">שם העובד:</span>
                            <span>{selectedPayslipData.employee.firstName} {selectedPayslipData.employee.lastName}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="font-medium">ת.ז/דרכון:</span>
                            <span>{selectedPayslipData.employee.nationalId}</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="font-medium">מס׳ עובד:</span>
                            <span>{selectedPayslipData.employee.id.slice(-6)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="font-medium">תאריך:</span>
                            <span>{format(new Date(selectedPayslipData.issuedAt), "dd/MM/yyyy", { locale: he })}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Main Payslip Table */}
                    <div className="border rounded-lg overflow-hidden">
                      <table className="w-full text-sm">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="text-right p-3 font-medium">תיאור</th>
                            <th className="text-center p-3 font-medium">קוד</th>
                            <th className="text-center p-3 font-medium">כמות</th>
                            <th className="text-center p-3 font-medium">תעריף</th>
                            <th className="text-left p-3 font-medium">סכום</th>
                          </tr>
                        </thead>
                        <tbody>
                          {/* Earnings Section */}
                          <tr className="bg-blue-50">
                            <td colSpan={5} className="p-2 font-semibold">תשלומים</td>
                          </tr>
                          {selectedPayslipData.items
                            ?.filter(item => item.type === "EARNING")
                            .map((item, index) => (
                              <tr key={index} className="border-b">
                                <td className="p-3">{item.description}</td>
                                <td className="p-3 text-center">{item.kod || "-"}</td>
                                <td className="p-3 text-center">{item.units ? item.units.toString() : "-"}</td>
                                <td className="p-3 text-center">{item.rate ? formatCurrency(item.rate) : "-"}</td>
                                <td className="p-3 text-left font-medium">{formatCurrency(item.amount)}</td>
                              </tr>
                            ))}
                          
                          {/* Deductions Section */}
                          <tr className="bg-red-50 border-t-2">
                            <td colSpan={5} className="p-2 font-semibold">ניכויים</td>
                          </tr>
                          {selectedPayslipData.items
                            ?.filter(item => item.type === "DEDUCTION")
                            .map((item, index) => (
                              <tr key={index} className="border-b">
                                <td className="p-3">{item.description}</td>
                                <td className="p-3 text-center">{item.kod || "-"}</td>
                                <td className="p-3 text-center">-</td>
                                <td className="p-3 text-center">-</td>
                                <td className="p-3 text-left font-medium text-red-600">
                                  -{formatCurrency(Math.abs(Number(item.amount)))}
                                </td>
                              </tr>
                            ))}

                          {/* Employer Contributions */}
                          {selectedPayslipData.items?.some(item => item.type === "EMPLOYER_CONTRIB") && (
                            <>
                              <tr className="bg-green-50 border-t-2">
                                <td colSpan={5} className="p-2 font-semibold">הפרשות מעסיק</td>
                              </tr>
                              {selectedPayslipData.items
                                ?.filter(item => item.type === "EMPLOYER_CONTRIB")
                                .map((item, index) => (
                                  <tr key={index} className="border-b">
                                    <td className="p-3">{item.description}</td>
                                    <td className="p-3 text-center">{item.kod || "-"}</td>
                                    <td className="p-3 text-center">-</td>
                                    <td className="p-3 text-center">-</td>
                                    <td className="p-3 text-left font-medium">{formatCurrency(item.amount)}</td>
                                  </tr>
                                ))}
                            </>
                          )}
                        </tbody>
                      </table>
                    </div>

                    {/* Summary Box */}
                    <div className="bg-gray-100 p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="font-medium">שכר ברוטו:</span>
                            <span className="font-bold">{formatCurrency(selectedPayslipData.grossPay)}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>מס הכנסה:</span>
                            <span>{formatCurrency(selectedPayslipData.taxDeducted)}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>ביטוח לאומי:</span>
                            <span>{formatCurrency(selectedPayslipData.insuranceDeducted)}</span>
                          </div>
                        </div>
                        <div className="flex items-end justify-end">
                          <div className="text-right">
                            <div className="text-sm text-gray-600">שכר נטו לתשלום:</div>
                            <div className="text-2xl font-bold text-blue-600">
                              {formatCurrency(selectedPayslipData.netPay)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="text-center text-xs text-gray-500 pt-4 border-t">
                      <p>מסמך זה הופק באופן ממוחשב ואינו דורש חתימה</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="py-16 text-center text-muted-foreground">
                <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>בחר תלוש שכר לתצוגה מקדימה</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
} 