# סיכום שילוב מערכת SMS

הקובץ `src/server/SMS_README.md` מפרט כיצד שולחת המערכת הודעות SMS לעובדים דרך Inforu.

## תכונות עיקריות
- שליחת הודעות GET או POST בהתאם לאורך ההודעה
- תבניות הודעה עם משתנים דינמיים
- תמיכה בשליחה מרוכזת למספר עובדים
- רישום מלא של כל הודעה בטבלת `SmsLog` וב־Audit Log
- בדיקת פורמט מספרי טלפון ישראליים והמרה ל־972

## דוגמת שימוש בסיסית
```typescript
const formatted = formatPhoneForSMS('0501234567');
await sendTemplatedSMS(formatted, SMS_TEMPLATES.PAYSLIP_READY, {
  firstName: 'יוסי',
  month: '12',
  year: '2024'
}, employeeId, tenantId, userId, userEmail);
```

## שיקולי אבטחה ותחזוקה
- יש להעביר את נתוני החיבור לשירות בקובץ `.env`
- מומלץ להגביל קצב שליחת הודעות כדי למנוע ספאם
- יש לעקוב אחרי הלוגים לוודא שאין שימוש בלתי מורשה
